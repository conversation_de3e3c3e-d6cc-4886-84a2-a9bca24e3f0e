"""Debug text extraction to see actual API responses."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_text_extraction():
    """Debug the text extraction to see what the API returns."""
    print("🔍 Debugging Text Extraction")
    print("=" * 50)
    
    try:
        from src.config import config
        from euriai import EuriaiClient
        
        # Initialize client
        client = EuriaiClient(
            api_key=config.EURI_API_KEY,
            model=config.DEFAULT_LLM_MODEL
        )
        
        # Simple test prompt
        test_prompt = """
Extract health indicators from this text and return them in JSON format:

"In Kakamega County, stunting prevalence among children under 5 was 22.3% in 2022."

Return JSON like this:
{
    "indicators": [
        {
            "name": "Stunting prevalence",
            "county": "Kakamega", 
            "year": 2022,
            "value": 22.3,
            "unit": "%"
        }
    ]
}
"""
        
        print("Sending test prompt...")
        response = client.generate_completion(
            prompt=test_prompt,
            temperature=0.1,
            max_tokens=500
        )
        
        print(f"\nResponse type: {type(response)}")
        print(f"Response content:")
        print("=" * 30)
        print(response)
        print("=" * 30)
        
        # Try to parse if it's a string
        if isinstance(response, str):
            import json
            import re
            
            # Look for JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                print(f"\nExtracted JSON:")
                print(json_str)
                
                try:
                    data = json.loads(json_str)
                    print(f"\nParsed successfully!")
                    print(f"Keys: {list(data.keys())}")
                    if 'indicators' in data:
                        print(f"Indicators found: {len(data['indicators'])}")
                except json.JSONDecodeError as e:
                    print(f"JSON parse error: {e}")
            else:
                print("No JSON found in response")
        
        elif isinstance(response, dict):
            print(f"\nResponse is already a dict!")
            print(f"Keys: {list(response.keys())}")
            if 'indicators' in response:
                print(f"Indicators found: {len(response['indicators'])}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_text_extraction()
