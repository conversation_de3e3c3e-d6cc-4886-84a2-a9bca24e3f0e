"""Vector store tool for storing and retrieving embeddings."""

import os
import pickle
import numpy as np
import faiss
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging

from ..models.embedding import EmbeddingRecord, VectorSearchRequest, VectorSearchResponse, QueryResult
from ..config import config

logger = logging.getLogger(__name__)


class VectorStoreTool:
    """Tool for managing vector storage and retrieval using FAISS."""
    
    def __init__(self):
        """Initialize the vector store."""
        self.store_path = config.VECTOR_STORE_PATH
        self.store_path.mkdir(parents=True, exist_ok=True)
        
        self.index_file = self.store_path / "faiss_index.bin"
        self.metadata_file = self.store_path / "metadata.pkl"
        
        self.index = None
        self.metadata = {}
        self.dimension = None
        
        # Load existing index if available
        self.load_index()
    
    def add_records(self, records: List[EmbeddingRecord]) -> None:
        """
        Add embedding records to the vector store.
        
        Args:
            records: List of EmbeddingRecord objects to add
        """
        if not records:
            return
        
        logger.info(f"Adding {len(records)} records to vector store")
        
        # Extract embeddings and metadata
        embeddings = np.array([record.embedding for record in records], dtype=np.float32)
        
        # Initialize index if needed
        if self.index is None:
            self.dimension = embeddings.shape[1]
            self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
            logger.info(f"Created new FAISS index with dimension {self.dimension}")
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        
        # Add to index
        start_id = len(self.metadata)
        self.index.add(embeddings)
        
        # Store metadata
        for i, record in enumerate(records):
            record_id = start_id + i
            self.metadata[record_id] = record.to_dict()
        
        logger.info(f"Added {len(records)} records. Total records: {len(self.metadata)}")
        
        # Save index and metadata
        self.save_index()
    
    def search(self, request: VectorSearchRequest) -> VectorSearchResponse:
        """
        Search for similar vectors.
        
        Args:
            request: Search request parameters
            
        Returns:
            Search response with results
        """
        import time
        start_time = time.time()
        
        if self.index is None or len(self.metadata) == 0:
            return VectorSearchResponse(
                query=request.query_text,
                results=[],
                total_results=0,
                search_time_ms=0.0
            )
        
        # Generate query embedding (this would need the embedding generator)
        # For now, we'll assume the query_text is already an embedding
        # In practice, you'd call the embedding generator here
        
        # Placeholder: return empty results for now
        # This will be completed when we integrate with the query agent
        
        search_time = (time.time() - start_time) * 1000
        
        return VectorSearchResponse(
            query=request.query_text,
            results=[],
            total_results=0,
            search_time_ms=search_time
        )
    
    def search_by_embedding(self, query_embedding: List[float], top_k: int = 10, 
                          filters: Optional[Dict[str, Any]] = None) -> List[QueryResult]:
        """
        Search by embedding vector.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of results to return
            filters: Optional filters to apply
            
        Returns:
            List of query results
        """
        if self.index is None or len(self.metadata) == 0:
            return []
        
        # Convert to numpy array and normalize
        query_vector = np.array([query_embedding], dtype=np.float32)
        faiss.normalize_L2(query_vector)
        
        # Search
        scores, indices = self.index.search(query_vector, min(top_k, len(self.metadata)))
        
        # Convert to results
        results = []
        for rank, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx == -1:  # FAISS returns -1 for invalid indices
                continue
                
            if idx in self.metadata:
                record_data = self.metadata[idx]
                record = EmbeddingRecord.from_dict(record_data)
                
                # Apply filters if specified
                if filters and not self._matches_filters(record, filters):
                    continue
                
                result = QueryResult(
                    record=record,
                    similarity_score=float(score),
                    rank=rank + 1
                )
                results.append(result)
        
        return results
    
    def _matches_filters(self, record: EmbeddingRecord, filters: Dict[str, Any]) -> bool:
        """
        Check if record matches the given filters.
        
        Args:
            record: EmbeddingRecord to check
            filters: Filter criteria
            
        Returns:
            True if record matches all filters
        """
        if 'counties' in filters and filters['counties']:
            if record.county not in filters['counties']:
                return False
        
        if 'years' in filters and filters['years']:
            if record.year not in filters['years']:
                return False
        
        if 'indicator_types' in filters and filters['indicator_types']:
            if record.indicator_type not in filters['indicator_types']:
                return False
        
        return True
    
    def save_index(self) -> None:
        """Save the FAISS index and metadata to disk."""
        try:
            if self.index is not None:
                faiss.write_index(self.index, str(self.index_file))
            
            with open(self.metadata_file, 'wb') as f:
                pickle.dump(self.metadata, f)
            
            logger.debug(f"Saved vector store with {len(self.metadata)} records")
            
        except Exception as e:
            logger.error(f"Failed to save vector store: {str(e)}")
            raise
    
    def load_index(self) -> None:
        """Load the FAISS index and metadata from disk."""
        try:
            if self.index_file.exists():
                self.index = faiss.read_index(str(self.index_file))
                self.dimension = self.index.d
                logger.info(f"Loaded FAISS index with dimension {self.dimension}")
            
            if self.metadata_file.exists():
                with open(self.metadata_file, 'rb') as f:
                    self.metadata = pickle.load(f)
                logger.info(f"Loaded metadata for {len(self.metadata)} records")
            
        except Exception as e:
            logger.warning(f"Failed to load existing vector store: {str(e)}")
            self.index = None
            self.metadata = {}
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the vector store.
        
        Returns:
            Dictionary with store statistics
        """
        stats = {
            "total_records": len(self.metadata),
            "dimension": self.dimension,
            "index_exists": self.index is not None,
            "store_path": str(self.store_path)
        }
        
        if self.metadata:
            # Count by county
            counties = {}
            years = {}
            types = {}
            
            for record_data in self.metadata.values():
                county = record_data.get('county', 'Unknown')
                year = record_data.get('year', 0)
                indicator_type = record_data.get('indicator_type', 'unknown')
                
                counties[county] = counties.get(county, 0) + 1
                years[year] = years.get(year, 0) + 1
                types[indicator_type] = types.get(indicator_type, 0) + 1
            
            stats.update({
                "counties": len(counties),
                "years": len(years),
                "indicator_types": len(types),
                "top_counties": sorted(counties.items(), key=lambda x: x[1], reverse=True)[:5],
                "years_range": [min(years.keys()), max(years.keys())] if years else [0, 0]
            })
        
        return stats
    
    def clear(self) -> None:
        """Clear all data from the vector store."""
        self.index = None
        self.metadata = {}
        self.dimension = None
        
        # Remove files
        if self.index_file.exists():
            self.index_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()
        
        logger.info("Cleared vector store")
