# Euriai API Configuration
EURI_API_KEY=your_euriai_api_key_here

# Model Configuration
DEFAULT_LLM_MODEL=gpt-4.1-nano
DEFAULT_EMBEDDING_MODEL=text-embedding-3-small

# Vector Store Configuration
VECTOR_STORE_TYPE=faiss  # Options: faiss, chroma, pgvector
VECTOR_STORE_PATH=./data/vector_store

# Application Configuration
APP_NAME=DHS RAQ System
APP_VERSION=1.0.0
LOG_LEVEL=INFO

# Streamlit Configuration
STREAMLIT_PORT=8501
STREAMLIT_HOST=localhost

# Data Paths
PDF_UPLOAD_PATH=./data/uploads
EXTRACTED_DATA_PATH=./data/extracted
GEOJSON_PATH=./data/geo/kenya_counties.geojson

# Processing Configuration
MAX_PDF_SIZE_MB=100
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_TOKENS_PER_REQUEST=4000

# Visualization Configuration
DEFAULT_MAP_CENTER_LAT=0.0236
DEFAULT_MAP_CENTER_LON=37.9062
DEFAULT_MAP_ZOOM=6
