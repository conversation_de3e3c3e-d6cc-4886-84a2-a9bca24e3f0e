"""Text extraction and indicator identification tool."""

import re
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from euriai import EuriaiClient
from ..models.extraction import PDFPage, PageType
from ..models.indicator import Indicator, IndicatorType, IndicatorSource
from ..models.county import KENYA_COUNTIES
from ..config import config

logger = logging.getLogger(__name__)


class TextAgentTool:
    """Tool for extracting indicators from text-heavy pages."""
    
    def __init__(self):
        """Initialize the text agent."""
        self.client = EuriaiClient(
            api_key=config.EURI_API_KEY,
            model=config.DEFAULT_LLM_MODEL
        )
        
        # County names for matching
        self.county_names = list(KENYA_COUNTIES.keys())
        
        # Indicator extraction prompt
        self.extraction_prompt = """
        Extract health indicators from this DHS (Demographic and Health Survey) text content.
        
        Look for:
        1. Health statistics and percentages
        2. County-specific data
        3. Year/survey period information
        4. Indicator names and values
        5. Age groups, gender specifications
        6. Urban/rural breakdowns
        
        Known Kenya counties: {counties}
        
        Text content:
        {content}
        
        Extract indicators in this JSON format:
        {{
            "indicators": [
                {{
                    "name": "indicator name",
                    "type": "indicator_type",
                    "county": "county name",
                    "year": 2022,
                    "value": 25.3,
                    "unit": "%",
                    "age_group": "15-49",
                    "gender": "women",
                    "urban_rural": "total",
                    "note": "additional context",
                    "summary": "brief summary for embedding"
                }}
            ]
        }}
        
        Indicator types: child_health, maternal_health, nutrition, immunization, family_planning, hiv_aids, malaria, water_sanitation, demographic, socioeconomic, other
        
        Only extract clear, specific indicators with county and value information.
        """
    
    def extract_indicators(self, page: PDFPage, document_name: str) -> List[Indicator]:
        """
        Extract indicators from a text-heavy page.
        
        Args:
            page: PDFPage object with text content
            document_name: Name of source document
            
        Returns:
            List of extracted Indicator objects
        """
        if not page.text_content or page.page_type not in [PageType.TEXT_HEAVY, PageType.MIXED]:
            return []
        
        try:
            # Prepare content for extraction
            content = self._prepare_text_content(page.text_content)
            
            # Check if content likely contains indicators
            if not self._has_indicator_content(content):
                logger.debug(f"Page {page.page_number} does not appear to contain indicators")
                return []
            
            prompt = self.extraction_prompt.format(
                counties=", ".join(self.county_names[:20]),  # First 20 counties
                content=content
            )
            
            # Get AI extraction
            response = self.client.generate_completion(
                prompt=prompt,
                temperature=0.2,  # Low temperature for consistent extraction
                max_tokens=2000
            )
            
            # Parse response
            indicators = self._parse_extraction_response(response, page, document_name)
            
            logger.info(f"Extracted {len(indicators)} indicators from page {page.page_number}")
            return indicators
            
        except Exception as e:
            logger.error(f"Failed to extract indicators from page {page.page_number}: {str(e)}")
            return []
    
    def extract_indicators_batch(self, pages: List[PDFPage], document_name: str) -> List[Indicator]:
        """
        Extract indicators from multiple pages.
        
        Args:
            pages: List of PDFPage objects
            document_name: Name of source document
            
        Returns:
            List of all extracted indicators
        """
        all_indicators = []
        
        text_pages = [p for p in pages if p.page_type in [PageType.TEXT_HEAVY, PageType.MIXED]]
        logger.info(f"Processing {len(text_pages)} text pages for indicator extraction")
        
        for page in text_pages:
            indicators = self.extract_indicators(page, document_name)
            all_indicators.extend(indicators)
        
        logger.info(f"Total indicators extracted from text: {len(all_indicators)}")
        return all_indicators
    
    def _prepare_text_content(self, text_content: str, max_chars: int = 3000) -> str:
        """
        Prepare text content for indicator extraction.
        
        Args:
            text_content: Raw text content
            max_chars: Maximum characters to process
            
        Returns:
            Cleaned and prepared text
        """
        # Clean up text
        text = re.sub(r'\s+', ' ', text_content)  # Normalize whitespace
        text = re.sub(r'[^\w\s\.\,\%\(\)\-\:]', '', text)  # Remove special chars
        
        # Truncate if too long
        if len(text) > max_chars:
            text = text[:max_chars] + "..."
        
        return text.strip()
    
    def _has_indicator_content(self, content: str) -> bool:
        """
        Check if content likely contains health indicators.
        
        Args:
            content: Text content to check
            
        Returns:
            True if content appears to contain indicators
        """
        content_lower = content.lower()
        
        # Look for indicator keywords
        indicator_keywords = [
            'percent', 'percentage', '%', 'rate', 'ratio',
            'stunting', 'wasting', 'underweight', 'immunization',
            'vaccination', 'mortality', 'fertility', 'contraceptive',
            'malaria', 'hiv', 'aids', 'anemia', 'nutrition'
        ]
        
        # Look for county names
        county_mentions = sum(1 for county in self.county_names 
                            if county.lower() in content_lower)
        
        # Look for numerical data
        numbers = re.findall(r'\d+\.?\d*', content)
        
        # Scoring
        keyword_score = sum(1 for keyword in indicator_keywords 
                          if keyword in content_lower)
        
        return (keyword_score >= 2 and county_mentions >= 1 and len(numbers) >= 3)
    
    def _parse_extraction_response(self, response: str, page: PDFPage, document_name: str) -> List[Indicator]:
        """
        Parse AI extraction response into Indicator objects.
        
        Args:
            response: AI response text
            page: Source page
            document_name: Source document name
            
        Returns:
            List of Indicator objects
        """
        indicators = []
        
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                logger.warning(f"No JSON found in extraction response for page {page.page_number}")
                return []
            
            json_str = json_match.group(0)
            data = json.loads(json_str)
            
            if 'indicators' not in data:
                logger.warning(f"No 'indicators' key found in response for page {page.page_number}")
                return []
            
            for ind_data in data['indicators']:
                try:
                    indicator = self._create_indicator_from_data(ind_data, page, document_name)
                    if indicator:
                        indicators.append(indicator)
                except Exception as e:
                    logger.warning(f"Failed to create indicator from data: {str(e)}")
                    continue
            
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON from extraction response: {str(e)}")
        except Exception as e:
            logger.error(f"Error parsing extraction response: {str(e)}")
        
        return indicators
    
    def _create_indicator_from_data(self, data: Dict[str, Any], page: PDFPage, document_name: str) -> Optional[Indicator]:
        """
        Create Indicator object from extracted data.
        
        Args:
            data: Extracted indicator data
            page: Source page
            document_name: Source document name
            
        Returns:
            Indicator object or None if invalid
        """
        try:
            # Validate required fields
            if not all(key in data for key in ['name', 'county', 'year']):
                return None
            
            # Validate county name
            county = self._normalize_county_name(data['county'])
            if not county:
                return None
            
            # Map indicator type
            indicator_type = self._map_indicator_type(data.get('type', 'other'))
            
            # Create source information
            source = IndicatorSource(
                document=document_name,
                page_number=page.page_number,
                section=f"Page {page.page_number}"
            )
            
            # Create indicator
            indicator = Indicator(
                name=data['name'],
                type=indicator_type,
                county=county,
                year=int(data['year']),
                value=float(data['value']) if data.get('value') is not None else None,
                value_text=str(data.get('value', '')),
                unit=data.get('unit'),
                age_group=data.get('age_group'),
                gender=data.get('gender'),
                urban_rural=data.get('urban_rural'),
                source=source,
                note=data.get('note'),
                extraction_method="TextAgentTool",
                extraction_confidence=0.8,
                summary=data.get('summary', f"{data['name']} in {county} ({data['year']})")
            )
            
            return indicator
            
        except Exception as e:
            logger.warning(f"Failed to create indicator: {str(e)}")
            return None
    
    def _normalize_county_name(self, county_name: str) -> Optional[str]:
        """
        Normalize and validate county name.
        
        Args:
            county_name: Raw county name
            
        Returns:
            Normalized county name or None if invalid
        """
        county_name = county_name.strip().title()
        
        # Direct match
        if county_name in self.county_names:
            return county_name
        
        # Fuzzy matching
        county_lower = county_name.lower()
        for county in self.county_names:
            if county.lower() == county_lower:
                return county
            if county_lower in county.lower() or county.lower() in county_lower:
                return county
        
        return None
    
    def _map_indicator_type(self, type_str: str) -> IndicatorType:
        """
        Map string to IndicatorType enum.
        
        Args:
            type_str: Type string
            
        Returns:
            IndicatorType enum value
        """
        try:
            return IndicatorType(type_str.lower())
        except ValueError:
            # Map common variations
            type_mapping = {
                'child': IndicatorType.CHILD_HEALTH,
                'maternal': IndicatorType.MATERNAL_HEALTH,
                'nutrition': IndicatorType.NUTRITION,
                'immunization': IndicatorType.IMMUNIZATION,
                'vaccination': IndicatorType.IMMUNIZATION,
                'family_planning': IndicatorType.FAMILY_PLANNING,
                'contraceptive': IndicatorType.FAMILY_PLANNING,
                'hiv': IndicatorType.HIV_AIDS,
                'aids': IndicatorType.HIV_AIDS,
                'malaria': IndicatorType.MALARIA,
                'water': IndicatorType.WATER_SANITATION,
                'sanitation': IndicatorType.WATER_SANITATION,
                'demographic': IndicatorType.DEMOGRAPHIC,
                'socioeconomic': IndicatorType.SOCIOECONOMIC,
            }
            
            type_lower = type_str.lower()
            for key, indicator_type in type_mapping.items():
                if key in type_lower:
                    return indicator_type
            
            return IndicatorType.OTHER
