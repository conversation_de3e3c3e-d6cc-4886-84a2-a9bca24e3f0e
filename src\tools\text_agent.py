"""Advanced text extraction and indicator identification tool with comprehensive prompting."""

import re
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from euriai import EuriaiClient
from ..models.extraction import PDFPage, PageType
from ..models.indicator import Indicator, IndicatorType, IndicatorSource
from ..models.county import KENYA_COUNTIES
from ..config import config

logger = logging.getLogger(__name__)


class TextAgentTool:
    """Advanced tool for extracting health indicators from text using comprehensive prompting."""

    def __init__(self):
        """Initialize the text agent with robust prompting strategies."""
        self.client = EuriaiClient(
            api_key=config.EURI_API_KEY,
            model=config.DEFAULT_LLM_MODEL
        )

        # County names for matching
        self.county_names = list(KENYA_COUNTIES.keys())

        # Comprehensive indicator extraction prompt with detailed instructions
        self.extraction_prompt = """
You are an expert data extraction specialist for Kenya's Demographic and Health Survey (DHS) reports. Your task is to extract precise health indicators from text content with maximum accuracy and completeness.

## EXTRACTION GUIDELINES:

### 1. INDICATOR IDENTIFICATION
Look for these types of health indicators:
- **Child Health**: stunting, wasting, underweight, child mortality, neonatal mortality
- **Maternal Health**: maternal mortality, antenatal care, skilled birth attendance, postnatal care
- **Nutrition**: malnutrition, anemia, vitamin deficiency, breastfeeding practices
- **Immunization**: vaccination coverage (BCG, DPT, polio, measles, pentavalent)
- **Family Planning**: contraceptive use, unmet need, birth spacing
- **HIV/AIDS**: HIV prevalence, testing, treatment, prevention
- **Malaria**: prevalence, treatment, prevention (ITN use, IRS)
- **Water/Sanitation**: access to improved water, sanitation facilities
- **Demographics**: fertility rates, population characteristics
- **Socioeconomic**: education, wealth quintiles, employment

### 2. DATA EXTRACTION RULES
- Extract ONLY indicators with specific numerical values (percentages, rates, counts)
- Include confidence intervals when mentioned
- Capture age group specifications (e.g., "15-49 years", "under 5", "6-59 months")
- Note gender specifications (men, women, children, all)
- Identify urban/rural breakdowns when present
- Extract sample sizes when mentioned

### 3. COUNTY IDENTIFICATION
Valid Kenya counties: {counties}
- Match county names exactly (case-insensitive)
- Handle variations like "County" suffix
- Skip indicators without clear county attribution

### 4. TEMPORAL INFORMATION
- Extract survey year (typically 2022, 2014, 2008-09, 2003)
- Note if data refers to trends or comparisons across years

### 5. QUALITY ASSURANCE
- Only extract indicators with clear numerical values
- Verify county names against the provided list
- Ensure indicator names are specific and descriptive
- Include relevant context in notes

## INPUT TEXT:
{content}

## OUTPUT FORMAT:
Return a JSON object with extracted indicators:

```json
{{
    "indicators": [
        {{
            "name": "Specific indicator name (e.g., 'Stunting prevalence among children under 5')",
            "type": "indicator_category",
            "county": "Exact county name from list",
            "year": 2022,
            "value": 25.3,
            "unit": "% or per 1000 or count",
            "age_group": "Specific age range if mentioned",
            "gender": "men/women/children/all",
            "urban_rural": "urban/rural/total",
            "confidence_interval": {{"lower": 22.1, "upper": 28.5}},
            "sample_size": 1250,
            "note": "Additional context or methodology notes",
            "summary": "One-sentence summary for embedding: 'Indicator X in County Y was Z% in Year'"
        }}
    ],
    "extraction_metadata": {{
        "total_indicators_found": 0,
        "counties_mentioned": [],
        "years_mentioned": [],
        "confidence_level": "high/medium/low",
        "extraction_notes": "Any issues or observations"
    }}
}}
```

## INDICATOR TYPE MAPPING:
- child_health: Child mortality, neonatal care, child development
- maternal_health: Maternal mortality, antenatal care, delivery care
- nutrition: Stunting, wasting, underweight, anemia, feeding practices
- immunization: Vaccination coverage for any vaccine
- family_planning: Contraceptive use, family planning services
- hiv_aids: HIV prevalence, testing, treatment, knowledge
- malaria: Malaria prevalence, treatment, prevention measures
- water_sanitation: Water access, sanitation facilities, hygiene
- demographic: Population, fertility, mortality demographics
- socioeconomic: Education, wealth, employment indicators
- other: Any health indicator not fitting above categories

## CRITICAL REQUIREMENTS:
1. Extract ONLY indicators with specific numerical values
2. Verify county names against the provided list
3. Include confidence intervals when available
4. Be precise with indicator names and descriptions
5. Provide meaningful summaries for embedding generation
6. Set confidence level based on data clarity and completeness

Begin extraction:
"""

    def extract_indicators(self, page: PDFPage, document_name: str) -> List[Indicator]:
        """
        Extract indicators from a text-heavy page.

        Args:
            page: PDFPage object with text content
            document_name: Name of source document

        Returns:
            List of extracted Indicator objects
        """
        if not page.text_content or page.page_type not in [PageType.TEXT_HEAVY, PageType.MIXED]:
            return []

        try:
            # Prepare content for extraction
            content = self._prepare_text_content(page.text_content)

            # Check if content likely contains indicators
            if not self._has_indicator_content(content):
                logger.debug(
                    f"Page {page.page_number} does not appear to contain indicators")
                return []

            # Format comprehensive prompt
            prompt = self.extraction_prompt.format(
                counties=", ".join(self.county_names),
                content=content
            )

            # Get AI extraction with optimized parameters
            response = self.client.generate_completion(
                prompt=prompt,
                temperature=0.1,  # Very low temperature for consistent extraction
                max_tokens=3000   # Increased for comprehensive responses
            )

            # Extract content from response
            if isinstance(response, dict) and 'choices' in response:
                content = response['choices'][0]['message']['content']
            else:
                content = response

            # Parse response with robust error handling
            indicators = self._parse_extraction_response(
                content, page, document_name)

            logger.info(
                f"Extracted {len(indicators)} indicators from page {page.page_number}")
            return indicators

        except Exception as e:
            logger.error(
                f"Failed to extract indicators from page {page.page_number}: {str(e)}")
            return []

    def extract_indicators_batch(self, pages: List[PDFPage], document_name: str) -> List[Indicator]:
        """
        Extract indicators from multiple pages.

        Args:
            pages: List of PDFPage objects
            document_name: Name of source document

        Returns:
            List of all extracted indicators
        """
        all_indicators = []

        text_pages = [p for p in pages if p.page_type in [
            PageType.TEXT_HEAVY, PageType.MIXED]]
        logger.info(
            f"Processing {len(text_pages)} text pages for indicator extraction")

        for page in text_pages:
            indicators = self.extract_indicators(page, document_name)
            all_indicators.extend(indicators)

        logger.info(
            f"Total indicators extracted from text: {len(all_indicators)}")
        return all_indicators

    def _prepare_text_content(self, text_content: str, max_chars: int = 4000) -> str:
        """
        Prepare text content for indicator extraction with enhanced cleaning.

        Args:
            text_content: Raw text content
            max_chars: Maximum characters to process

        Returns:
            Cleaned and prepared text
        """
        # Enhanced text cleaning
        text = re.sub(r'\s+', ' ', text_content)  # Normalize whitespace
        # Remove special chars but keep more punctuation
        text = re.sub(r'[^\w\s\.\,\%\(\)\-\:\;\[\]\/]', '', text)
        text = re.sub(r'\b\d+\.\d+\b', lambda m: m.group(),
                      text)  # Preserve decimal numbers

        # Intelligent truncation - try to keep complete sentences
        if len(text) > max_chars:
            truncated = text[:max_chars]
            last_period = truncated.rfind('.')
            if last_period > max_chars * 0.8:  # If we can find a period in the last 20%
                text = truncated[:last_period + 1]
            else:
                text = truncated + "..."

        return text.strip()

    def _has_indicator_content(self, content: str) -> bool:
        """
        Enhanced check if content likely contains health indicators.

        Args:
            content: Text content to check

        Returns:
            True if content appears to contain indicators
        """
        content_lower = content.lower()

        # Comprehensive indicator keywords
        indicator_keywords = [
            # Measurements
            'percent', 'percentage', '%', 'rate', 'ratio', 'prevalence', 'coverage',
            'incidence', 'mortality', 'morbidity', 'per 1000', 'per 100',

            # Health indicators
            'stunting', 'wasting', 'underweight', 'immunization', 'vaccination',
            'antenatal', 'postnatal', 'delivery', 'contraceptive', 'family planning',
            'malaria', 'hiv', 'aids', 'anemia', 'nutrition', 'breastfeeding',
            'water', 'sanitation', 'hygiene', 'skilled birth', 'maternal mortality',

            # Survey terms
            'survey', 'dhs', 'demographic', 'health', 'indicator', 'data'
        ]

        # County name detection (more flexible)
        county_mentions = 0
        for county in self.county_names:
            if county.lower() in content_lower:
                county_mentions += 1

        # Number detection (percentages, decimals, rates)
        numbers = re.findall(
            r'\d+\.?\d*\s*(?:%|percent|per\s+1000|per\s+100)', content_lower)

        # Scoring system
        keyword_score = sum(
            1 for keyword in indicator_keywords if keyword in content_lower)

        # More sophisticated scoring
        has_keywords = keyword_score >= 3
        has_counties = county_mentions >= 1
        has_numbers = len(numbers) >= 2
        has_survey_context = any(term in content_lower for term in [
                                 'dhs', 'survey', 'demographic'])

        return has_keywords and has_counties and has_numbers

    def _parse_extraction_response(self, response: str, page: PDFPage, document_name: str) -> List[Indicator]:
        """
        Parse AI extraction response into Indicator objects.

        Args:
            response: AI response text
            page: Source page
            document_name: Source document name

        Returns:
            List of Indicator objects
        """
        indicators = []

        try:
            # Handle both string and dict responses
            if isinstance(response, dict):
                data = response
            elif isinstance(response, str):
                # Try to extract JSON from response string
                json_str = self._extract_json_from_response(response)
                if not json_str:
                    logger.warning(
                        f"No valid JSON found in extraction response for page {page.page_number}")
                    return []
                data = json.loads(json_str)
            else:
                logger.warning(f"Unexpected response type: {type(response)}")
                return []

            if 'indicators' not in data:
                logger.warning(
                    f"No 'indicators' key found in response for page {page.page_number}")
                return []

            for ind_data in data['indicators']:
                try:
                    indicator = self._create_indicator_from_data(
                        ind_data, page, document_name)
                    if indicator:
                        indicators.append(indicator)
                except Exception as e:
                    logger.warning(
                        f"Failed to create indicator from data: {str(e)}")
                    continue

        except json.JSONDecodeError as e:
            logger.warning(
                f"Failed to parse JSON from extraction response: {str(e)}")
        except Exception as e:
            logger.error(f"Error parsing extraction response: {str(e)}")

        return indicators

    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """Extract JSON from response using multiple strategies."""
        # Strategy 1: Look for JSON code block
        json_match = re.search(
            r'```json\s*(\{.*?\})\s*```', response, re.DOTALL)
        if json_match:
            return json_match.group(1)

        # Strategy 2: Look for any JSON object
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json_match.group(0)

        # Strategy 3: Try to find JSON starting with "indicators"
        json_match = re.search(r'\{[^}]*"indicators".*\}', response, re.DOTALL)
        if json_match:
            return json_match.group(0)

        return None

    def _create_indicator_from_data(self, data: Dict[str, Any], page: PDFPage, document_name: str) -> Optional[Indicator]:
        """
        Create Indicator object from extracted data.

        Args:
            data: Extracted indicator data
            page: Source page
            document_name: Source document name

        Returns:
            Indicator object or None if invalid
        """
        try:
            # Validate required fields
            if not all(key in data for key in ['name', 'county', 'year']):
                return None

            # Validate county name
            county = self._normalize_county_name(data['county'])
            if not county:
                return None

            # Map indicator type
            indicator_type = self._map_indicator_type(
                data.get('type', 'other'))

            # Create source information
            source = IndicatorSource(
                document=document_name,
                page_number=page.page_number,
                section=f"Page {page.page_number}"
            )

            # Create indicator
            indicator = Indicator(
                name=data['name'],
                type=indicator_type,
                county=county,
                year=int(data['year']),
                value=float(data['value']) if data.get(
                    'value') is not None else None,
                value_text=str(data.get('value', '')),
                unit=data.get('unit'),
                age_group=data.get('age_group'),
                gender=data.get('gender'),
                urban_rural=data.get('urban_rural'),
                source=source,
                note=data.get('note'),
                extraction_method="TextAgentTool",
                extraction_confidence=0.8,
                summary=data.get(
                    'summary', f"{data['name']} in {county} ({data['year']})")
            )

            return indicator

        except Exception as e:
            logger.warning(f"Failed to create indicator: {str(e)}")
            return None

    def _normalize_county_name(self, county_name: str) -> Optional[str]:
        """
        Normalize and validate county name.

        Args:
            county_name: Raw county name

        Returns:
            Normalized county name or None if invalid
        """
        county_name = county_name.strip().title()

        # Direct match
        if county_name in self.county_names:
            return county_name

        # Fuzzy matching
        county_lower = county_name.lower()
        for county in self.county_names:
            if county.lower() == county_lower:
                return county
            if county_lower in county.lower() or county.lower() in county_lower:
                return county

        return None

    def _map_indicator_type(self, type_str: str) -> IndicatorType:
        """
        Map string to IndicatorType enum.

        Args:
            type_str: Type string

        Returns:
            IndicatorType enum value
        """
        try:
            return IndicatorType(type_str.lower())
        except ValueError:
            # Map common variations
            type_mapping = {
                'child': IndicatorType.CHILD_HEALTH,
                'maternal': IndicatorType.MATERNAL_HEALTH,
                'nutrition': IndicatorType.NUTRITION,
                'immunization': IndicatorType.IMMUNIZATION,
                'vaccination': IndicatorType.IMMUNIZATION,
                'family_planning': IndicatorType.FAMILY_PLANNING,
                'contraceptive': IndicatorType.FAMILY_PLANNING,
                'hiv': IndicatorType.HIV_AIDS,
                'aids': IndicatorType.HIV_AIDS,
                'malaria': IndicatorType.MALARIA,
                'water': IndicatorType.WATER_SANITATION,
                'sanitation': IndicatorType.WATER_SANITATION,
                'demographic': IndicatorType.DEMOGRAPHIC,
                'socioeconomic': IndicatorType.SOCIOECONOMIC,
            }

            type_lower = type_str.lower()
            for key, indicator_type in type_mapping.items():
                if key in type_lower:
                    return indicator_type

            return IndicatorType.OTHER
