"""
DHS RAQ System - Streamlit Application

A comprehensive Retrieval-Augmented Questioning system for Kenya's Demographic and Health Survey data.
Enables natural language queries on health indicators with intelligent response generation.
"""

from src.models.query import QueryRequest
from src.tools.query_agent import QueryAgentTool
from src.tools.vector_store import VectorStoreTool
from src.tools.embedding_generator import EmbeddingGeneratorTool
from src.tools.text_agent import TextAgentTool
from src.tools.pdf_loader import PDFLoaderTool
from src.config import config
import streamlit as st
import sys
from pathlib import Path
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


# Page configuration
st.set_page_config(
    page_title="DHS RAQ System",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
    }
    .query-box {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .insight-box {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ffc107;
        margin-bottom: 1rem;
    }
    .success-box {
        background-color: #d4edda;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize session state variables."""
    if 'vector_store_ready' not in st.session_state:
        st.session_state.vector_store_ready = False
    if 'processed_indicators' not in st.session_state:
        st.session_state.processed_indicators = []
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []
    if 'current_response' not in st.session_state:
        st.session_state.current_response = None


def check_api_key():
    """Check if API key is configured."""
    if not config.EURI_API_KEY or config.EURI_API_KEY == "your_euriai_api_key_here":
        st.error("🔑 **API Key Required**")
        st.markdown("""
        Please set your EURI_API_KEY in the `.env` file to use the RAQ system.
        
        1. Open `.env` file in the project root
        2. Replace `your_euriai_api_key_here` with your actual API key
        3. Restart the application
        """)
        return False
    return True


def main():
    """Main application function."""
    initialize_session_state()

    # Header
    st.markdown('<div class="main-header">🏥 DHS RAQ System</div>',
                unsafe_allow_html=True)
    st.markdown('<div style="text-align: center; color: #666; margin-bottom: 2rem;">Retrieval-Augmented Questioning for Kenya Demographic and Health Survey Data</div>', unsafe_allow_html=True)

    # Check API key
    if not check_api_key():
        return

    # Sidebar
    with st.sidebar:
        st.markdown("## 🔧 System Controls")

        # System status
        st.markdown("### 📊 System Status")

        # Vector store status
        vector_status = "✅ Ready" if st.session_state.vector_store_ready else "⚠️ Not Ready"
        st.markdown(f"**Vector Store:** {vector_status}")

        indicators_count = len(st.session_state.processed_indicators)
        st.markdown(f"**Indicators:** {indicators_count}")

        # Data management
        st.markdown("### 📁 Data Management")

        if st.button("🔄 Reset System", help="Clear all processed data"):
            st.session_state.vector_store_ready = False
            st.session_state.processed_indicators = []
            st.session_state.query_history = []
            st.session_state.current_response = None
            st.success("System reset successfully!")
            st.rerun()

        # Query history
        if st.session_state.query_history:
            st.markdown("### 📝 Recent Queries")
            for i, query in enumerate(reversed(st.session_state.query_history[-5:])):
                with st.expander(f"Query {len(st.session_state.query_history) - i}"):
                    st.write(f"**Q:** {query['query']}")
                    st.write(f"**Time:** {query['timestamp']}")
                    if query.get('indicators_found', 0) > 0:
                        st.write(
                            f"**Results:** {query['indicators_found']} indicators")

    # Main content area
    tab1, tab2, tab3, tab4 = st.tabs(
        ["🔍 Query Interface", "📄 PDF Processing", "📊 Data Explorer", "ℹ️ About"])

    with tab1:
        show_query_interface()

    with tab2:
        show_pdf_processing()

    with tab3:
        show_data_explorer()

    with tab4:
        show_about()


def show_query_interface():
    """Show the main query interface."""
    st.markdown('<div class="sub-header">🔍 Natural Language Query Interface</div>',
                unsafe_allow_html=True)

    if not st.session_state.vector_store_ready:
        st.warning(
            "⚠️ **Vector store not ready.** Please process some PDFs first in the 'PDF Processing' tab.")
        return

    # Query input
    st.markdown("### Ask Questions About Health Indicators")

    # Example queries
    with st.expander("💡 Example Queries"):
        example_queries = [
            "What is the stunting prevalence in Western Kenya?",
            "Compare immunization coverage between Kakamega and Vihiga",
            "Show me child health indicators for 2022",
            "What are the maternal health statistics?",
            "Compare nutrition indicators across counties"
        ]

        for query in example_queries:
            if st.button(f"📝 {query}", key=f"example_{hash(query)}"):
                st.session_state.current_query = query

    # Query input box
    query = st.text_area(
        "Enter your question:",
        value=st.session_state.get('current_query', ''),
        height=100,
        placeholder="e.g., What is the stunting prevalence in Kakamega compared to Vihiga?",
        help="Ask natural language questions about health indicators in Kenya"
    )

    # Query options
    col1, col2, col3 = st.columns(3)
    with col1:
        include_charts = st.checkbox("📊 Include Charts", value=True)
    with col2:
        include_maps = st.checkbox("🗺️ Include Maps", value=True)
    with col3:
        include_summary = st.checkbox("📝 Include Summary", value=True)

    # Process query
    if st.button("🚀 Process Query", type="primary", disabled=not query.strip()):
        process_query(query, include_charts, include_maps, include_summary)

    # Display results
    if st.session_state.current_response:
        display_query_results(st.session_state.current_response)


def process_query(query: str, include_charts: bool, include_maps: bool, include_summary: bool):
    """Process a natural language query."""
    with st.spinner("🤖 Processing your query..."):
        try:
            # Initialize query agent
            query_agent = QueryAgentTool()

            # Create request
            request = QueryRequest(
                query=query,
                include_charts=include_charts,
                include_maps=include_maps,
                include_summary=include_summary,
                max_results=20
            )

            # Process query
            response = query_agent.process_query(request)

            # Store response
            st.session_state.current_response = response

            # Add to history
            st.session_state.query_history.append({
                'query': query,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'indicators_found': response.total_indicators,
                'processing_time': response.processing_time_ms
            })

            st.success(
                f"✅ Query processed in {response.processing_time_ms:.1f}ms")

        except Exception as e:
            st.error(f"❌ Error processing query: {str(e)}")


def display_query_results(response):
    """Display query results."""
    st.markdown("## 📋 Query Results")

    # Metrics
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Indicators Found", response.total_indicators)
    with col2:
        st.metric("Processing Time", f"{response.processing_time_ms:.1f}ms")
    with col3:
        st.metric("Confidence Score", f"{response.confidence_score:.2f}")
    with col4:
        st.metric("Comparisons", len(response.comparisons))

    # Executive Summary
    if response.summary:
        st.markdown("### 📝 Executive Summary")
        st.markdown(
            f'<div class="success-box">{response.summary}</div>', unsafe_allow_html=True)

    # Key Insights
    if response.insights:
        st.markdown("### 💡 Key Insights")
        for i, insight in enumerate(response.insights, 1):
            st.markdown(
                f'<div class="insight-box"><strong>Insight {i}:</strong> {insight}</div>', unsafe_allow_html=True)

    # Indicators Table
    if response.indicators:
        st.markdown("### 📊 Detailed Indicators")

        # Convert to DataFrame
        indicators_data = []
        for ind in response.indicators:
            indicators_data.append({
                'Indicator': ind.name,
                'County': ind.county,
                'Year': ind.year,
                'Value': ind.value,
                'Unit': ind.unit or '',
                'Type': ind.type.value,
                'Age Group': ind.age_group or 'All',
                'Gender': ind.gender or 'All'
            })

        df = pd.DataFrame(indicators_data)
        st.dataframe(df, use_container_width=True)

        # Download button
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 Download Results as CSV",
            data=csv,
            file_name=f"dhs_indicators_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    # Charts
    if response.chart_data and response.chart_data.get('data'):
        st.markdown("### 📈 Visualization")

        chart_data = response.chart_data

        # Create chart based on data
        if chart_data['type'] == 'bar':
            fig = px.bar(
                x=chart_data['labels'],
                y=chart_data['data'],
                title=chart_data['title'],
                labels={'x': 'County',
                        'y': f"Value ({chart_data.get('unit', '')})"}
            )
            st.plotly_chart(fig, use_container_width=True)

    # Comparisons
    if response.comparisons:
        st.markdown("### 🔍 Detailed Comparisons")

        for comp in response.comparisons:
            with st.expander(f"📊 {comp.indicator_name}"):
                st.write(comp.summary)

                if comp.comparisons:
                    comp_df = pd.DataFrame(comp.comparisons)
                    st.dataframe(comp_df, use_container_width=True)

                # Statistics
                if comp.mean_value is not None:
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("Mean", f"{comp.mean_value:.2f}")
                    with col2:
                        st.metric(
                            "Std Dev", f"{comp.std_deviation:.2f}" if comp.std_deviation else "N/A")
                    with col3:
                        st.metric(
                            "Min", f"{comp.min_value:.2f}" if comp.min_value else "N/A")
                    with col4:
                        st.metric(
                            "Max", f"{comp.max_value:.2f}" if comp.max_value else "N/A")


def show_pdf_processing():
    """Show PDF processing interface."""
    st.markdown('<div class="sub-header">📄 PDF Processing & Data Extraction</div>',
                unsafe_allow_html=True)

    # File upload
    st.markdown("### 📁 Upload DHS PDF Documents")

    uploaded_files = st.file_uploader(
        "Choose PDF files",
        type=['pdf'],
        accept_multiple_files=True,
        help="Upload Kenya DHS PDF documents for processing"
    )

    # Processing options
    st.markdown("### ⚙️ Processing Options")
    col1, col2 = st.columns(2)
    with col1:
        max_pages = st.slider("Max pages to process", 1, 50,
                              10, help="Limit processing for faster results")
    with col2:
        extract_tables = st.checkbox(
            "Extract tables", value=True, help="Extract data from tables (coming soon)")

    # Process uploaded files
    if uploaded_files and st.button("🚀 Process PDFs", type="primary"):
        process_uploaded_pdfs(uploaded_files, max_pages)

    # Show existing PDFs
    st.markdown("### 📚 Available PDF Files")

    pdf_files = list(config.PDF_UPLOAD_PATH.glob("*.pdf"))
    if pdf_files:
        for pdf_file in pdf_files:
            size_mb = pdf_file.stat().st_size / (1024 * 1024)
            col1, col2, col3 = st.columns([3, 1, 1])
            with col1:
                st.write(f"📄 {pdf_file.name}")
            with col2:
                st.write(f"{size_mb:.1f} MB")
            with col3:
                if st.button("Process", key=f"process_{pdf_file.name}"):
                    process_existing_pdf(pdf_file, max_pages)
    else:
        st.info(
            "No PDF files found. Upload files above or place them in the data/uploads folder.")


def process_uploaded_pdfs(uploaded_files, max_pages):
    """Process uploaded PDF files."""
    progress_bar = st.progress(0)
    status_text = st.empty()

    all_indicators = []

    for i, uploaded_file in enumerate(uploaded_files):
        status_text.text(f"Processing {uploaded_file.name}...")

        # Save uploaded file
        file_path = config.PDF_UPLOAD_PATH / uploaded_file.name
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())

        # Process PDF
        indicators = process_pdf_file(file_path, max_pages)
        all_indicators.extend(indicators)

        progress_bar.progress((i + 1) / len(uploaded_files))

    # Update vector store
    if all_indicators:
        update_vector_store(all_indicators)
        st.success(
            f"✅ Processed {len(uploaded_files)} files, extracted {len(all_indicators)} indicators")
    else:
        st.warning("⚠️ No indicators extracted from uploaded files")

    status_text.empty()
    progress_bar.empty()


def process_existing_pdf(pdf_file, max_pages):
    """Process an existing PDF file."""
    with st.spinner(f"Processing {pdf_file.name}..."):
        indicators = process_pdf_file(pdf_file, max_pages)

        if indicators:
            update_vector_store(indicators)
            st.success(
                f"✅ Extracted {len(indicators)} indicators from {pdf_file.name}")
        else:
            st.warning(f"⚠️ No indicators extracted from {pdf_file.name}")


def process_pdf_file(file_path, max_pages):
    """Process a single PDF file and extract indicators."""
    try:
        # Initialize tools
        pdf_loader = PDFLoaderTool()
        text_agent = TextAgentTool()

        # Load PDF
        pdf_result = pdf_loader.load_pdf(str(file_path))

        # Process text pages (limited by max_pages)
        text_pages = [p for p in pdf_result.pages if p.page_type.value in [
            'text_heavy', 'mixed']][:max_pages]

        all_indicators = []
        for page in text_pages:
            indicators = text_agent.extract_indicators(page, file_path.name)
            all_indicators.extend(indicators)

        return all_indicators

    except Exception as e:
        st.error(f"Error processing {file_path.name}: {str(e)}")
        return []


def update_vector_store(indicators):
    """Update the vector store with new indicators."""
    try:
        embedding_generator = EmbeddingGeneratorTool()
        vector_store = VectorStoreTool()

        # Generate embeddings
        embedding_records = embedding_generator.create_embedding_records_batch(
            indicators)

        # Add to vector store
        vector_store.add_records(embedding_records)

        # Update session state
        st.session_state.processed_indicators.extend(indicators)
        st.session_state.vector_store_ready = True

    except Exception as e:
        st.error(f"Error updating vector store: {str(e)}")


def show_data_explorer():
    """Show data exploration interface."""
    st.markdown('<div class="sub-header">📊 Data Explorer</div>',
                unsafe_allow_html=True)

    if not st.session_state.processed_indicators:
        st.info("📋 No data available. Please process some PDFs first.")
        return

    indicators = st.session_state.processed_indicators

    # Summary statistics
    st.markdown("### 📈 Data Summary")

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Indicators", len(indicators))
    with col2:
        counties = set(ind.county for ind in indicators)
        st.metric("Counties", len(counties))
    with col3:
        years = set(ind.year for ind in indicators)
        st.metric("Years", len(years))
    with col4:
        types = set(ind.type.value for ind in indicators)
        st.metric("Indicator Types", len(types))

    # Filters
    st.markdown("### 🔍 Data Filters")

    col1, col2, col3 = st.columns(3)

    with col1:
        selected_counties = st.multiselect(
            "Counties",
            options=sorted(counties),
            default=list(counties)[:5] if len(counties) > 5 else list(counties)
        )

    with col2:
        selected_years = st.multiselect(
            "Years",
            options=sorted(years, reverse=True),
            default=list(years)
        )

    with col3:
        selected_types = st.multiselect(
            "Indicator Types",
            options=sorted(types),
            default=list(types)
        )

    # Filter data
    filtered_indicators = [
        ind for ind in indicators
        if (ind.county in selected_counties and
            ind.year in selected_years and
            ind.type.value in selected_types)
    ]

    st.write(f"Showing {len(filtered_indicators)} indicators")

    # Data table
    if filtered_indicators:
        st.markdown("### 📋 Indicator Data")

        # Convert to DataFrame
        data = []
        for ind in filtered_indicators:
            data.append({
                'Indicator': ind.name,
                'County': ind.county,
                'Year': ind.year,
                'Value': ind.value,
                'Unit': ind.unit or '',
                'Type': ind.type.value,
                'Age Group': ind.age_group or 'All',
                'Gender': ind.gender or 'All'
            })

        df = pd.DataFrame(data)
        st.dataframe(df, use_container_width=True)

        # Visualizations
        if len(filtered_indicators) > 1:
            st.markdown("### 📊 Quick Visualizations")

            # Group by county
            county_counts = df['County'].value_counts()
            if len(county_counts) > 1:
                fig = px.bar(
                    x=county_counts.index,
                    y=county_counts.values,
                    title="Indicators by County",
                    labels={'x': 'County', 'y': 'Number of Indicators'}
                )
                st.plotly_chart(fig, use_container_width=True)

            # Group by type
            type_counts = df['Type'].value_counts()
            if len(type_counts) > 1:
                fig = px.pie(
                    values=type_counts.values,
                    names=type_counts.index,
                    title="Indicators by Type"
                )
                st.plotly_chart(fig, use_container_width=True)


def show_about():
    """Show about page."""
    st.markdown('<div class="sub-header">ℹ️ About DHS RAQ System</div>',
                unsafe_allow_html=True)

    st.markdown("""
    ## 🎯 System Overview

    The **DHS RAQ (Retrieval-Augmented Questioning) System** is an advanced AI-powered platform designed to make Kenya's Demographic and Health Survey data more accessible and actionable through natural language queries.

    ## 🚀 Key Features

    ### 📄 **Intelligent PDF Processing**
    - Automated extraction of health indicators from DHS PDF documents
    - Advanced text analysis with AI-powered content understanding
    - Support for multiple document formats and structures

    ### 🧠 **Natural Language Querying**
    - Ask questions in plain English about health indicators
    - Intelligent query understanding and context extraction
    - Support for complex comparisons and trend analysis

    ### 📊 **Comprehensive Analytics**
    - Automated generation of insights and recommendations
    - County-level comparisons and rankings
    - Interactive visualizations and charts

    ### 🔍 **Semantic Search**
    - Vector-based similarity search using advanced embeddings
    - Context-aware retrieval of relevant health indicators
    - High-precision matching of query intent to data

    ## 🏗️ Technical Architecture

    ### **Core Components:**
    - **PDF Processing**: PyMuPDF for document parsing
    - **AI Models**: Euriai SDK for embeddings and text generation
    - **Vector Storage**: FAISS for efficient similarity search
    - **UI Framework**: Streamlit for interactive web interface

    ### **Data Pipeline:**
    1. **PDF Upload** → Document parsing and page classification
    2. **Text Extraction** → AI-powered indicator identification
    3. **Embedding Generation** → Vector representation creation
    4. **Vector Storage** → Efficient indexing for fast retrieval
    5. **Query Processing** → Natural language understanding
    6. **Response Generation** → Comprehensive answer synthesis

    ## 📈 **Use Cases**

    ### **For Researchers:**
    - Quick access to specific health indicators
    - Cross-county comparisons and trend analysis
    - Data validation and quality assessment

    ### **For Policymakers:**
    - Evidence-based decision making
    - Identification of health disparities
    - Resource allocation optimization

    ### **For Health Professionals:**
    - Program evaluation and monitoring
    - Baseline data for interventions
    - Performance benchmarking

    ## 🔧 **System Requirements**

    - **API Key**: Euriai API key for AI functionality
    - **Storage**: Sufficient space for PDF documents and vector indices
    - **Memory**: Recommended 4GB+ RAM for optimal performance

    ## 📞 **Support & Documentation**

    For technical support, feature requests, or documentation:
    - Check the README.md file in the project repository
    - Review the configuration settings in the .env file
    - Ensure all dependencies are properly installed

    ## 🎉 **Getting Started**

    1. **Upload PDFs**: Use the 'PDF Processing' tab to upload DHS documents
    2. **Process Data**: Extract indicators and build the knowledge base
    3. **Ask Questions**: Use the 'Query Interface' to ask natural language questions
    4. **Explore Results**: Review insights, comparisons, and visualizations

    ---

    **Version**: 1.0.0 | **Built with**: Python, Streamlit, Euriai SDK | **License**: MIT
    """)


if __name__ == "__main__":
    main()
