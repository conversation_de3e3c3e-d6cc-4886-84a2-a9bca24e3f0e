"""Advanced page classification tool using AI."""

import re
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from euriai import EuriaiClient
from ..models.extraction import PDFPage, PageType
from ..config import config

logger = logging.getLogger(__name__)


class PageClassifierTool:
    """Tool for classifying PDF pages using AI."""
    
    def __init__(self):
        """Initialize the page classifier."""
        self.client = EuriaiClient(
            api_key=config.EURI_API_KEY,
            model=config.DEFAULT_LLM_MODEL
        )
        
        # Classification prompts
        self.classification_prompt = """
        Analyze this PDF page content and classify it into one of these categories:
        
        1. TEXT_HEAVY: Pages with primarily narrative text, paragraphs, explanations
        2. TABLE_HEAVY: Pages with structured data tables, statistics, numerical data
        3. IMAGE_HEAVY: Pages with charts, graphs, maps, photos, or visual content
        4. MIXED: Pages with combination of text, tables, and images
        5. COVER: Title pages, cover pages, document headers
        6. TOC: Table of contents, index pages
        7. APPENDIX: Appendix sections, reference materials
        8. UNKNOWN: Cannot determine or unclear content
        
        Page content:
        {content}
        
        Additional context:
        - Page number: {page_number}
        - Images detected: {images_detected}
        - Tables detected: {tables_detected}
        - Text length: {text_length} characters
        
        Respond with only the category name (e.g., "TABLE_HEAVY") and a brief 1-sentence explanation.
        Format: CATEGORY: explanation
        """
    
    def classify_page(self, page: PDFPage) -> PDFPage:
        """
        Classify a single page using AI.
        
        Args:
            page: PDFPage object to classify
            
        Returns:
            Updated PDFPage with refined classification
        """
        if not page.text_content or len(page.text_content.strip()) < 10:
            # Very little text content
            if page.images_detected > 0:
                page.page_type = PageType.IMAGE_HEAVY
            else:
                page.page_type = PageType.UNKNOWN
            return page
        
        try:
            # Prepare content for classification
            content_sample = self._prepare_content_sample(page.text_content)
            
            prompt = self.classification_prompt.format(
                content=content_sample,
                page_number=page.page_number,
                images_detected=page.images_detected,
                tables_detected=page.tables_detected,
                text_length=len(page.text_content)
            )
            
            # Get AI classification
            response = self.client.generate_completion(
                prompt=prompt,
                temperature=0.1,  # Low temperature for consistent classification
                max_tokens=100
            )
            
            # Parse response
            classified_type, explanation = self._parse_classification_response(response)
            
            # Update page with new classification
            page.page_type = classified_type
            
            # Update confidence score based on classification quality
            page.confidence_score = self._calculate_confidence(page, explanation)
            
            logger.debug(f"Page {page.page_number} classified as {classified_type.value}: {explanation}")
            
        except Exception as e:
            logger.warning(f"Failed to classify page {page.page_number}: {str(e)}")
            # Keep original basic classification
        
        return page
    
    def classify_pages_batch(self, pages: List[PDFPage]) -> List[PDFPage]:
        """
        Classify multiple pages efficiently.
        
        Args:
            pages: List of PDFPage objects to classify
            
        Returns:
            List of updated PDFPage objects
        """
        logger.info(f"Classifying {len(pages)} pages")
        
        classified_pages = []
        for page in pages:
            classified_page = self.classify_page(page)
            classified_pages.append(classified_page)
        
        # Log classification summary
        type_counts = {}
        for page in classified_pages:
            page_type = page.page_type.value
            type_counts[page_type] = type_counts.get(page_type, 0) + 1
        
        logger.info(f"Classification summary: {type_counts}")
        
        return classified_pages
    
    def _prepare_content_sample(self, text_content: str, max_chars: int = 2000) -> str:
        """
        Prepare a representative sample of page content for classification.
        
        Args:
            text_content: Full text content
            max_chars: Maximum characters to include
            
        Returns:
            Prepared content sample
        """
        if len(text_content) <= max_chars:
            return text_content
        
        # Take beginning and end of content
        half_chars = max_chars // 2
        beginning = text_content[:half_chars]
        ending = text_content[-half_chars:]
        
        return f"{beginning}\n\n[... content truncated ...]\n\n{ending}"
    
    def _parse_classification_response(self, response: str) -> tuple[PageType, str]:
        """
        Parse AI classification response.
        
        Args:
            response: AI response text
            
        Returns:
            Tuple of (PageType, explanation)
        """
        try:
            # Look for pattern: CATEGORY: explanation
            match = re.match(r'([A-Z_]+):\s*(.+)', response.strip())
            
            if match:
                category_str = match.group(1)
                explanation = match.group(2)
                
                # Map to PageType enum
                try:
                    page_type = PageType(category_str.lower())
                    return page_type, explanation
                except ValueError:
                    # Invalid category, try to map common variations
                    category_mapping = {
                        'TEXT': PageType.TEXT_HEAVY,
                        'TABLE': PageType.TABLE_HEAVY,
                        'IMAGE': PageType.IMAGE_HEAVY,
                        'CHART': PageType.IMAGE_HEAVY,
                        'GRAPH': PageType.IMAGE_HEAVY,
                        'MAP': PageType.IMAGE_HEAVY,
                    }
                    
                    for key, page_type in category_mapping.items():
                        if key in category_str:
                            return page_type, explanation
            
            # Fallback: analyze response content
            response_lower = response.lower()
            if 'table' in response_lower or 'data' in response_lower:
                return PageType.TABLE_HEAVY, "Contains tabular data"
            elif 'image' in response_lower or 'chart' in response_lower or 'graph' in response_lower:
                return PageType.IMAGE_HEAVY, "Contains visual content"
            elif 'cover' in response_lower or 'title' in response_lower:
                return PageType.COVER, "Appears to be cover page"
            elif 'contents' in response_lower or 'index' in response_lower:
                return PageType.TOC, "Table of contents"
            else:
                return PageType.TEXT_HEAVY, "Primarily text content"
                
        except Exception as e:
            logger.warning(f"Failed to parse classification response: {str(e)}")
            return PageType.UNKNOWN, "Classification failed"
    
    def _calculate_confidence(self, page: PDFPage, explanation: str) -> float:
        """
        Calculate confidence score for classification.
        
        Args:
            page: Classified page
            explanation: AI explanation
            
        Returns:
            Confidence score (0.0 to 1.0)
        """
        base_confidence = 0.7
        
        # Boost confidence for clear indicators
        if page.page_type == PageType.TABLE_HEAVY and page.tables_detected > 0:
            base_confidence += 0.2
        elif page.page_type == PageType.IMAGE_HEAVY and page.images_detected > 0:
            base_confidence += 0.2
        elif page.page_type == PageType.TEXT_HEAVY and len(page.text_content or "") > 500:
            base_confidence += 0.1
        
        # Reduce confidence for uncertain explanations
        uncertain_words = ['might', 'could', 'possibly', 'unclear', 'uncertain']
        if any(word in explanation.lower() for word in uncertain_words):
            base_confidence -= 0.2
        
        return min(1.0, max(0.1, base_confidence))
