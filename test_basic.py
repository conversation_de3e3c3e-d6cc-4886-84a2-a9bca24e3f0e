"""Basic test to check imports and setup."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")

    try:
        # Test config
        from src.config import config
        print(f"✓ Config loaded: {config.APP_NAME}")

        # Test models
        from src.models.indicator import Indicator, IndicatorType, IndicatorSource
        from src.models.county import KENYA_COUNTIES
        print(f"✓ Models loaded, {len(KENYA_COUNTIES)} counties available")

        # Test tools
        from src.tools.pdf_loader import PDFLoaderTool
        from src.tools.vector_store import VectorStoreTool
        print("✓ Tools loaded")

        # Test euriai
        import euriai
        print("✓ Euriai SDK available")

        # Test other dependencies
        import fitz
        import faiss
        print("✓ PyMuPDF and FAISS available")

        print("\n🎉 All basic imports successful!")
        return True

    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        return False


def test_pdf_files():
    """Check available PDF files."""
    print("\nChecking PDF files...")

    from src.config import config
    pdf_files = list(config.PDF_UPLOAD_PATH.glob("*.pdf"))

    if pdf_files:
        print(f"✓ Found {len(pdf_files)} PDF files:")
        for pdf in pdf_files:
            size_mb = pdf.stat().st_size / (1024 * 1024)
            print(f"  - {pdf.name} ({size_mb:.1f} MB)")
    else:
        print("⚠ No PDF files found in uploads folder")

    return len(pdf_files) > 0


def test_geo_files():
    """Check GeoJSON files."""
    print("\nChecking GeoJSON files...")

    from src.config import config
    geo_files = list(config.GEOJSON_PATH.parent.glob("*.json"))

    if geo_files:
        print(f"✓ Found {len(geo_files)} GeoJSON files")
        # Test loading one
        import json
        with open(geo_files[0], 'r', encoding='utf-8') as f:
            data = json.load(f)
            if 'centroid' in data:
                print("✓ GeoJSON structure looks correct")
    else:
        print("⚠ No GeoJSON files found")

    return len(geo_files) > 0


if __name__ == "__main__":
    print("🚀 DHS RAQ System - Basic Setup Test")
    print("=" * 50)

    success = True
    success &= test_basic_imports()
    success &= test_pdf_files()
    success &= test_geo_files()

    print("\n" + "=" * 50)
    if success:
        print("✅ Basic setup test PASSED!")
        print("Ready to run milestone tests.")
    else:
        print("❌ Basic setup test FAILED!")
        print("Please fix the issues before proceeding.")
