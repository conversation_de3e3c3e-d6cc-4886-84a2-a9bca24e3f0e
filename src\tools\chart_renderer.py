"""Chart rendering tool for creating visualizations from health indicator data."""

import json
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from ..models.indicator import Indicator, IndicatorType
from ..models.query import ChartData
from ..models.county import KENYA_COUNTIES
from ..config import config

logger = logging.getLogger(__name__)


class ChartRendererTool:
    """Tool for creating interactive charts and visualizations from health indicator data."""
    
    def __init__(self):
        """Initialize the chart renderer."""
        self.county_names = list(KENYA_COUNTIES.keys())
        
        # Color schemes for different chart types
        self.color_schemes = {
            'default': px.colors.qualitative.Set3,
            'health': ['#2E8B57', '#4682B4', '#DAA520', '#CD853F', '#9370DB'],
            'nutrition': ['#228B22', '#32CD32', '#ADFF2F', '#9AFF9A', '#98FB98'],
            'maternal': ['#FF69B4', '#FFB6C1', '#FFC0CB', '#FFCCCB', '#F0E68C'],
            'child': ['#87CEEB', '#87CEFA', '#B0E0E6', '#ADD8E6', '#E0F6FF'],
            'immunization': ['#4169E1', '#6495ED', '#7B68EE', '#9370DB', '#8A2BE2']
        }

    def create_chart(self, indicators: List[Indicator], chart_type: str = "auto", 
                    title: Optional[str] = None, **kwargs) -> ChartData:
        """
        Create a chart from health indicators.
        
        Args:
            indicators: List of indicators to visualize
            chart_type: Type of chart ("bar", "line", "pie", "scatter", "auto")
            title: Chart title
            **kwargs: Additional chart options
            
        Returns:
            ChartData object with chart configuration
        """
        if not indicators:
            return self._create_empty_chart("No data available")
        
        try:
            # Convert indicators to DataFrame
            df = self._indicators_to_dataframe(indicators)
            
            # Auto-select chart type if needed
            if chart_type == "auto":
                chart_type = self._auto_select_chart_type(df, indicators)
            
            # Create chart based on type
            if chart_type == "bar":
                return self._create_bar_chart(df, indicators, title, **kwargs)
            elif chart_type == "line":
                return self._create_line_chart(df, indicators, title, **kwargs)
            elif chart_type == "pie":
                return self._create_pie_chart(df, indicators, title, **kwargs)
            elif chart_type == "scatter":
                return self._create_scatter_chart(df, indicators, title, **kwargs)
            elif chart_type == "heatmap":
                return self._create_heatmap(df, indicators, title, **kwargs)
            elif chart_type == "comparison":
                return self._create_comparison_chart(df, indicators, title, **kwargs)
            else:
                logger.warning(f"Unknown chart type: {chart_type}, using bar chart")
                return self._create_bar_chart(df, indicators, title, **kwargs)
                
        except Exception as e:
            logger.error(f"Failed to create chart: {str(e)}")
            return self._create_empty_chart(f"Error creating chart: {str(e)}")

    def _indicators_to_dataframe(self, indicators: List[Indicator]) -> pd.DataFrame:
        """Convert indicators to pandas DataFrame."""
        data = []
        
        for indicator in indicators:
            data.append({
                'indicator_name': indicator.name,
                'county': indicator.county,
                'year': indicator.year,
                'value': indicator.value,
                'unit': indicator.unit or '',
                'type': indicator.type.value,
                'age_group': indicator.age_group or 'All',
                'gender': indicator.gender or 'All',
                'urban_rural': indicator.urban_rural or 'Total',
                'extraction_confidence': indicator.extraction_confidence or 0.5
            })
        
        return pd.DataFrame(data)

    def _auto_select_chart_type(self, df: pd.DataFrame, indicators: List[Indicator]) -> str:
        """Automatically select the best chart type based on data characteristics."""
        unique_counties = df['county'].nunique()
        unique_indicators = df['indicator_name'].nunique()
        unique_years = df['year'].nunique()
        
        # Decision logic for chart type
        if unique_years > 1 and unique_counties <= 5:
            return "line"  # Time series for few counties
        elif unique_counties > 10:
            return "heatmap"  # Too many counties for bar chart
        elif unique_indicators > 1:
            return "comparison"  # Multiple indicators
        elif unique_counties <= 8:
            return "bar"  # Standard bar chart
        else:
            return "bar"  # Default fallback

    def _create_bar_chart(self, df: pd.DataFrame, indicators: List[Indicator], 
                         title: Optional[str], **kwargs) -> ChartData:
        """Create a bar chart."""
        try:
            # Determine grouping
            if df['indicator_name'].nunique() == 1:
                # Single indicator, group by county
                x_col = 'county'
                y_col = 'value'
                color_col = None
                chart_title = title or f"{df['indicator_name'].iloc[0]} by County"
            else:
                # Multiple indicators, group by indicator
                x_col = 'indicator_name'
                y_col = 'value'
                color_col = 'county' if df['county'].nunique() <= 10 else None
                chart_title = title or "Health Indicators Comparison"
            
            # Get color scheme
            indicator_type = indicators[0].type.value if indicators else 'default'
            colors = self.color_schemes.get(indicator_type, self.color_schemes['default'])
            
            # Create figure
            if color_col:
                fig = px.bar(df, x=x_col, y=y_col, color=color_col,
                           title=chart_title,
                           color_discrete_sequence=colors)
            else:
                fig = px.bar(df, x=x_col, y=y_col,
                           title=chart_title,
                           color_discrete_sequence=colors)
            
            # Customize layout
            fig.update_layout(
                xaxis_title=x_col.replace('_', ' ').title(),
                yaxis_title=f"Value ({df['unit'].iloc[0] if df['unit'].iloc[0] else 'Units'})",
                showlegend=bool(color_col),
                height=500
            )
            
            # Rotate x-axis labels if too many
            if df[x_col].nunique() > 8:
                fig.update_xaxes(tickangle=45)
            
            return ChartData(
                type="bar",
                data=df.to_dict('records'),
                labels=df[x_col].tolist(),
                values=df[y_col].tolist(),
                title=chart_title,
                unit=df['unit'].iloc[0] if not df.empty else '',
                plotly_json=fig.to_json()
            )
            
        except Exception as e:
            logger.error(f"Failed to create bar chart: {str(e)}")
            return self._create_empty_chart("Error creating bar chart")

    def _create_line_chart(self, df: pd.DataFrame, indicators: List[Indicator], 
                          title: Optional[str], **kwargs) -> ChartData:
        """Create a line chart for time series data."""
        try:
            chart_title = title or "Health Indicators Over Time"
            
            # Group by county and indicator
            if df['county'].nunique() == 1:
                # Single county, multiple indicators or years
                fig = px.line(df, x='year', y='value', color='indicator_name',
                            title=chart_title)
            else:
                # Multiple counties
                fig = px.line(df, x='year', y='value', color='county',
                            title=chart_title)
            
            # Customize layout
            fig.update_layout(
                xaxis_title="Year",
                yaxis_title=f"Value ({df['unit'].iloc[0] if df['unit'].iloc[0] else 'Units'})",
                height=500
            )
            
            return ChartData(
                type="line",
                data=df.to_dict('records'),
                labels=df['year'].tolist(),
                values=df['value'].tolist(),
                title=chart_title,
                unit=df['unit'].iloc[0] if not df.empty else '',
                plotly_json=fig.to_json()
            )
            
        except Exception as e:
            logger.error(f"Failed to create line chart: {str(e)}")
            return self._create_empty_chart("Error creating line chart")

    def _create_pie_chart(self, df: pd.DataFrame, indicators: List[Indicator], 
                         title: Optional[str], **kwargs) -> ChartData:
        """Create a pie chart."""
        try:
            chart_title = title or "Health Indicators Distribution"
            
            # Use county distribution for pie chart
            if df['county'].nunique() > 1:
                # Aggregate by county
                county_data = df.groupby('county')['value'].mean().reset_index()
                fig = px.pie(county_data, values='value', names='county',
                           title=chart_title)
            else:
                # Use indicator distribution
                indicator_data = df.groupby('indicator_name')['value'].mean().reset_index()
                fig = px.pie(indicator_data, values='value', names='indicator_name',
                           title=chart_title)
            
            fig.update_layout(height=500)
            
            return ChartData(
                type="pie",
                data=df.to_dict('records'),
                labels=df['county'].tolist() if df['county'].nunique() > 1 else df['indicator_name'].tolist(),
                values=df['value'].tolist(),
                title=chart_title,
                unit=df['unit'].iloc[0] if not df.empty else '',
                plotly_json=fig.to_json()
            )
            
        except Exception as e:
            logger.error(f"Failed to create pie chart: {str(e)}")
            return self._create_empty_chart("Error creating pie chart")

    def _create_scatter_chart(self, df: pd.DataFrame, indicators: List[Indicator], 
                             title: Optional[str], **kwargs) -> ChartData:
        """Create a scatter plot."""
        try:
            chart_title = title or "Health Indicators Scatter Plot"
            
            # Use extraction confidence vs value
            fig = px.scatter(df, x='extraction_confidence', y='value', 
                           color='county', size='year',
                           hover_data=['indicator_name'],
                           title=chart_title)
            
            fig.update_layout(
                xaxis_title="Extraction Confidence",
                yaxis_title=f"Value ({df['unit'].iloc[0] if df['unit'].iloc[0] else 'Units'})",
                height=500
            )
            
            return ChartData(
                type="scatter",
                data=df.to_dict('records'),
                labels=df['county'].tolist(),
                values=df['value'].tolist(),
                title=chart_title,
                unit=df['unit'].iloc[0] if not df.empty else '',
                plotly_json=fig.to_json()
            )
            
        except Exception as e:
            logger.error(f"Failed to create scatter chart: {str(e)}")
            return self._create_empty_chart("Error creating scatter chart")

    def _create_heatmap(self, df: pd.DataFrame, indicators: List[Indicator], 
                       title: Optional[str], **kwargs) -> ChartData:
        """Create a heatmap."""
        try:
            chart_title = title or "Health Indicators Heatmap"
            
            # Pivot data for heatmap
            if df['indicator_name'].nunique() > 1 and df['county'].nunique() > 1:
                pivot_df = df.pivot_table(values='value', index='county', 
                                        columns='indicator_name', aggfunc='mean')
            else:
                # Fallback: county vs year
                pivot_df = df.pivot_table(values='value', index='county', 
                                        columns='year', aggfunc='mean')
            
            fig = px.imshow(pivot_df, 
                          title=chart_title,
                          color_continuous_scale='Viridis')
            
            fig.update_layout(height=600)
            
            return ChartData(
                type="heatmap",
                data=df.to_dict('records'),
                labels=pivot_df.index.tolist(),
                values=pivot_df.values.flatten().tolist(),
                title=chart_title,
                unit=df['unit'].iloc[0] if not df.empty else '',
                plotly_json=fig.to_json()
            )
            
        except Exception as e:
            logger.error(f"Failed to create heatmap: {str(e)}")
            return self._create_empty_chart("Error creating heatmap")

    def _create_comparison_chart(self, df: pd.DataFrame, indicators: List[Indicator], 
                                title: Optional[str], **kwargs) -> ChartData:
        """Create a comparison chart with multiple subplots."""
        try:
            chart_title = title or "Health Indicators Comparison"
            
            unique_indicators = df['indicator_name'].unique()
            n_indicators = len(unique_indicators)
            
            # Create subplots
            if n_indicators <= 2:
                rows, cols = 1, n_indicators
            elif n_indicators <= 4:
                rows, cols = 2, 2
            else:
                rows, cols = 3, 3
            
            fig = make_subplots(
                rows=rows, cols=cols,
                subplot_titles=unique_indicators[:rows*cols],
                specs=[[{"secondary_y": False} for _ in range(cols)] for _ in range(rows)]
            )
            
            for i, indicator in enumerate(unique_indicators[:rows*cols]):
                row = i // cols + 1
                col = i % cols + 1
                
                indicator_data = df[df['indicator_name'] == indicator]
                
                fig.add_trace(
                    go.Bar(x=indicator_data['county'], y=indicator_data['value'],
                          name=indicator, showlegend=False),
                    row=row, col=col
                )
            
            fig.update_layout(title_text=chart_title, height=600)
            
            return ChartData(
                type="comparison",
                data=df.to_dict('records'),
                labels=df['county'].unique().tolist(),
                values=df['value'].tolist(),
                title=chart_title,
                unit=df['unit'].iloc[0] if not df.empty else '',
                plotly_json=fig.to_json()
            )
            
        except Exception as e:
            logger.error(f"Failed to create comparison chart: {str(e)}")
            return self._create_empty_chart("Error creating comparison chart")

    def _create_empty_chart(self, message: str) -> ChartData:
        """Create an empty chart with error message."""
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(
            title="No Data Available",
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False),
            height=400
        )
        
        return ChartData(
            type="empty",
            data=[],
            labels=[],
            values=[],
            title="No Data Available",
            unit="",
            plotly_json=fig.to_json()
        )

    def create_multiple_charts(self, indicators: List[Indicator], 
                             chart_types: List[str] = None) -> List[ChartData]:
        """
        Create multiple charts from the same data.
        
        Args:
            indicators: List of indicators
            chart_types: List of chart types to create
            
        Returns:
            List of ChartData objects
        """
        if chart_types is None:
            chart_types = ["bar", "line", "pie"]
        
        charts = []
        for chart_type in chart_types:
            try:
                chart = self.create_chart(indicators, chart_type)
                charts.append(chart)
            except Exception as e:
                logger.error(f"Failed to create {chart_type} chart: {str(e)}")
                continue
        
        return charts

    def get_chart_recommendations(self, indicators: List[Indicator]) -> List[str]:
        """
        Get recommended chart types based on data characteristics.
        
        Args:
            indicators: List of indicators
            
        Returns:
            List of recommended chart types
        """
        if not indicators:
            return []
        
        df = self._indicators_to_dataframe(indicators)
        recommendations = []
        
        unique_counties = df['county'].nunique()
        unique_indicators = df['indicator_name'].nunique()
        unique_years = df['year'].nunique()
        
        # Always recommend bar chart
        recommendations.append("bar")
        
        # Time series if multiple years
        if unique_years > 1:
            recommendations.append("line")
        
        # Pie chart for distribution
        if unique_counties > 1 and unique_counties <= 8:
            recommendations.append("pie")
        
        # Heatmap for complex data
        if unique_counties > 5 and unique_indicators > 1:
            recommendations.append("heatmap")
        
        # Comparison for multiple indicators
        if unique_indicators > 1:
            recommendations.append("comparison")
        
        return recommendations
