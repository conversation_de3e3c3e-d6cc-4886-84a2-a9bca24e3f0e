"""Embedding and vector store data models."""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class EmbeddingRecord(BaseModel):
    """Record for storing embeddings in vector store."""
    
    id: str = Field(..., description="Unique identifier")
    embedding: List[float] = Field(..., description="Vector embedding")
    
    # Metadata for filtering and retrieval
    indicator_id: str = Field(..., description="Associated indicator ID")
    county: str = Field(..., description="County name")
    year: int = Field(..., description="Survey year")
    indicator_type: str = Field(..., description="Indicator type")
    indicator_name: str = Field(..., description="Indicator name")
    
    # Content for retrieval
    content: str = Field(..., description="Text content that was embedded")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    created_at: datetime = Field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "EmbeddingRecord":
        """Create from dictionary."""
        return cls(**data)


class QueryResult(BaseModel):
    """Result from vector similarity search."""
    
    record: EmbeddingRecord = Field(..., description="Retrieved embedding record")
    similarity_score: float = Field(..., description="Similarity score (0-1)")
    rank: int = Field(..., description="Rank in results (1-based)")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "record": self.record.to_dict(),
            "similarity_score": self.similarity_score,
            "rank": self.rank
        }


class VectorSearchRequest(BaseModel):
    """Request for vector similarity search."""
    
    query_text: str = Field(..., description="Query text to search for")
    top_k: int = Field(default=10, description="Number of results to return")
    
    # Filters
    counties: Optional[List[str]] = Field(None, description="Filter by counties")
    years: Optional[List[int]] = Field(None, description="Filter by years")
    indicator_types: Optional[List[str]] = Field(None, description="Filter by indicator types")
    
    # Search parameters
    similarity_threshold: float = Field(default=0.0, description="Minimum similarity score")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()


class VectorSearchResponse(BaseModel):
    """Response from vector similarity search."""
    
    query: str = Field(..., description="Original query")
    results: List[QueryResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    search_time_ms: float = Field(..., description="Search time in milliseconds")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "query": self.query,
            "results": [r.to_dict() for r in self.results],
            "total_results": self.total_results,
            "search_time_ms": self.search_time_ms
        }
