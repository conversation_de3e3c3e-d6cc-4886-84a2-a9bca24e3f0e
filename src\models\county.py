"""County and location data models."""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class CountyLocation(BaseModel):
    """Geographic location information for a county."""
    latitude: float = Field(..., description="Latitude coordinate")
    longitude: float = Field(..., description="Longitude coordinate")
    region: Optional[str] = Field(None, description="Administrative region")
    
    @validator('latitude')
    def validate_latitude(cls, v):
        """Validate latitude is within Kenya's bounds."""
        if not (-5.0 <= v <= 5.5):
            raise ValueError("Latitude must be within Kenya's bounds (-5.0 to 5.5)")
        return v
    
    @validator('longitude')
    def validate_longitude(cls, v):
        """Validate longitude is within Kenya's bounds."""
        if not (33.5 <= v <= 42.0):
            raise ValueError("Longitude must be within Kenya's bounds (33.5 to 42.0)")
        return v


class County(BaseModel):
    """County information model."""
    
    name: str = Field(..., description="County name")
    code: Optional[str] = Field(None, description="County code")
    location: CountyLocation = Field(..., description="Geographic location")
    population: Optional[int] = Field(None, description="Population estimate")
    area_km2: Optional[float] = Field(None, description="Area in square kilometers")
    
    # Administrative information
    capital: Optional[str] = Field(None, description="County capital/headquarters")
    governor: Optional[str] = Field(None, description="Current governor")
    
    # Economic indicators
    poverty_rate: Optional[float] = Field(None, description="Poverty rate percentage")
    literacy_rate: Optional[float] = Field(None, description="Literacy rate percentage")
    
    @validator('name')
    def validate_name(cls, v):
        """Ensure county name is properly formatted."""
        return v.strip().title()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "County":
        """Create from dictionary."""
        return cls(**data)


# Predefined Kenya counties with their locations
KENYA_COUNTIES = {
    "Mombasa": CountyLocation(latitude=-4.0435, longitude=39.6682, region="Coast"),
    "Kwale": CountyLocation(latitude=-4.1747, longitude=39.4502, region="Coast"),
    "Kilifi": CountyLocation(latitude=-3.5107, longitude=39.8498, region="Coast"),
    "Tana River": CountyLocation(latitude=-1.8530, longitude=40.1105, region="Coast"),
    "Lamu": CountyLocation(latitude=-2.2717, longitude=40.9020, region="Coast"),
    "Taita Taveta": CountyLocation(latitude=-3.3167, longitude=38.3500, region="Coast"),
    "Garissa": CountyLocation(latitude=-0.4536, longitude=39.6401, region="North Eastern"),
    "Wajir": CountyLocation(latitude=1.7471, longitude=40.0629, region="North Eastern"),
    "Mandera": CountyLocation(latitude=3.9366, longitude=41.8670, region="North Eastern"),
    "Marsabit": CountyLocation(latitude=2.3284, longitude=37.9899, region="North Eastern"),
    "Isiolo": CountyLocation(latitude=0.3556, longitude=37.5833, region="North Eastern"),
    "Meru": CountyLocation(latitude=0.0500, longitude=37.6500, region="Eastern"),
    "Tharaka Nithi": CountyLocation(latitude=-0.3667, longitude=37.9833, region="Eastern"),
    "Embu": CountyLocation(latitude=-0.5167, longitude=37.4500, region="Eastern"),
    "Kitui": CountyLocation(latitude=-1.3667, longitude=38.0167, region="Eastern"),
    "Machakos": CountyLocation(latitude=-1.5177, longitude=37.2634, region="Eastern"),
    "Makueni": CountyLocation(latitude=-1.8038, longitude=37.6244, region="Eastern"),
    "Nyandarua": CountyLocation(latitude=-0.3000, longitude=36.3667, region="Central"),
    "Nyeri": CountyLocation(latitude=-0.4167, longitude=36.9500, region="Central"),
    "Kirinyaga": CountyLocation(latitude=-0.6667, longitude=37.3000, region="Central"),
    "Murang'a": CountyLocation(latitude=-0.7167, longitude=37.1500, region="Central"),
    "Kiambu": CountyLocation(latitude=-1.1667, longitude=36.8333, region="Central"),
    "Turkana": CountyLocation(latitude=3.1167, longitude=35.6000, region="Rift Valley"),
    "West Pokot": CountyLocation(latitude=1.4000, longitude=35.1167, region="Rift Valley"),
    "Samburu": CountyLocation(latitude=1.1667, longitude=36.8000, region="Rift Valley"),
    "Trans Nzoia": CountyLocation(latitude=1.0167, longitude=34.9500, region="Rift Valley"),
    "Uasin Gishu": CountyLocation(latitude=0.5167, longitude=35.2833, region="Rift Valley"),
    "Elgeyo Marakwet": CountyLocation(latitude=0.8667, longitude=35.4833, region="Rift Valley"),
    "Nandi": CountyLocation(latitude=0.1833, longitude=35.1000, region="Rift Valley"),
    "Baringo": CountyLocation(latitude=0.4667, longitude=35.9667, region="Rift Valley"),
    "Laikipia": CountyLocation(latitude=0.2000, longitude=36.7833, region="Rift Valley"),
    "Nakuru": CountyLocation(latitude=-0.3000, longitude=36.0667, region="Rift Valley"),
    "Narok": CountyLocation(latitude=-1.0833, longitude=35.8667, region="Rift Valley"),
    "Kajiado": CountyLocation(latitude=-1.8500, longitude=36.7833, region="Rift Valley"),
    "Kericho": CountyLocation(latitude=-0.3667, longitude=35.2833, region="Rift Valley"),
    "Bomet": CountyLocation(latitude=-0.7833, longitude=35.3417, region="Rift Valley"),
    "Kakamega": CountyLocation(latitude=0.2827, longitude=34.7519, region="Western"),
    "Vihiga": CountyLocation(latitude=0.0667, longitude=34.7167, region="Western"),
    "Bungoma": CountyLocation(latitude=0.5635, longitude=34.5606, region="Western"),
    "Busia": CountyLocation(latitude=0.4667, longitude=34.1167, region="Western"),
    "Siaya": CountyLocation(latitude=0.0667, longitude=34.2833, region="Nyanza"),
    "Kisumu": CountyLocation(latitude=-0.1000, longitude=34.7500, region="Nyanza"),
    "Homa Bay": CountyLocation(latitude=-0.5167, longitude=34.4500, region="Nyanza"),
    "Migori": CountyLocation(latitude=-1.0634, longitude=34.4731, region="Nyanza"),
    "Kisii": CountyLocation(latitude=-0.6833, longitude=34.7667, region="Nyanza"),
    "Nyamira": CountyLocation(latitude=-0.5667, longitude=34.9333, region="Nyanza"),
    "Nairobi": CountyLocation(latitude=-1.2921, longitude=36.8219, region="Nairobi"),
}
