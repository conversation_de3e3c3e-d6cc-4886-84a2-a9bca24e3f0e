"""
Milestone 1 Test: Core Components Validation

This test validates:
1. Configuration loading
2. Data models creation and validation
3. PDF loading and basic extraction
4. Embedding generation
5. Vector store operations

Run this test to ensure core components are working before proceeding.
"""

import pytest
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config import config
from src.models.indicator import Indicator, IndicatorType, IndicatorSource
from src.models.county import County, CountyLocation, KENYA_COUNTIES
from src.models.embedding import EmbeddingRecord
from src.tools.pdf_loader import PDFLoaderTool
from src.tools.embedding_generator import EmbeddingGeneratorTool
from src.tools.vector_store import VectorStoreTool


class TestMilestone1:
    """Test core components functionality."""
    
    def test_config_loading(self):
        """Test that configuration loads properly."""
        print("\n=== Testing Configuration ===")
        
        # Check basic config values
        assert config.APP_NAME == "DHS RAQ System"
        assert config.VECTOR_STORE_TYPE == "faiss"
        
        # Check paths exist
        assert config.PDF_UPLOAD_PATH.exists()
        assert config.EXTRACTED_DATA_PATH.exists()
        assert config.VECTOR_STORE_PATH.exists()
        
        print(f"✓ Config loaded: {config.APP_NAME} v{config.APP_VERSION}")
        print(f"✓ Upload path: {config.PDF_UPLOAD_PATH}")
        print(f"✓ Vector store: {config.VECTOR_STORE_PATH}")
    
    def test_data_models(self):
        """Test data model creation and validation."""
        print("\n=== Testing Data Models ===")
        
        # Test County model
        location = CountyLocation(latitude=0.2827, longitude=34.7519, region="Western")
        county = County(name="Kakamega", location=location)
        assert county.name == "Kakamega"
        assert county.location.region == "Western"
        print("✓ County model works")
        
        # Test Indicator model
        source = IndicatorSource(document="test.pdf", page_number=1)
        indicator = Indicator(
            name="Stunting prevalence",
            type=IndicatorType.NUTRITION,
            county="Kakamega",
            year=2022,
            value=22.3,
            unit="%",
            source=source
        )
        assert indicator.county == "Kakamega"
        assert indicator.value == 22.3
        print("✓ Indicator model works")
        
        # Test embedding text generation
        embedding_text = indicator.to_embedding_text()
        assert "Kakamega" in embedding_text
        assert "2022" in embedding_text
        print("✓ Embedding text generation works")
        
        # Test Kenya counties data
        assert "Kakamega" in KENYA_COUNTIES
        assert len(KENYA_COUNTIES) == 47  # Kenya has 47 counties
        print(f"✓ Kenya counties loaded: {len(KENYA_COUNTIES)} counties")
    
    def test_pdf_loading(self):
        """Test PDF loading functionality."""
        print("\n=== Testing PDF Loading ===")
        
        # Check if PDFs exist
        pdf_files = list(config.PDF_UPLOAD_PATH.glob("*.pdf"))
        if not pdf_files:
            print("⚠ No PDF files found in uploads folder - skipping PDF test")
            return
        
        # Test with first PDF
        pdf_path = pdf_files[0]
        print(f"Testing with: {pdf_path.name}")
        
        loader = PDFLoaderTool()
        
        try:
            result = loader.load_pdf(str(pdf_path))
            
            assert result.total_pages > 0
            assert len(result.pages) > 0
            assert result.success_rate > 0
            
            print(f"✓ PDF loaded: {result.total_pages} pages")
            print(f"✓ Success rate: {result.success_rate:.1%}")
            print(f"✓ Processing time: {result.total_processing_time_ms:.1f}ms")
            
            # Check page types
            page_types = {}
            for page in result.pages:
                page_type = page.page_type.value
                page_types[page_type] = page_types.get(page_type, 0) + 1
            
            print(f"✓ Page types: {page_types}")
            
        except Exception as e:
            print(f"✗ PDF loading failed: {str(e)}")
            raise
    
    def test_embedding_generation(self):
        """Test embedding generation."""
        print("\n=== Testing Embedding Generation ===")
        
        # Check if API key is set
        if not config.EURI_API_KEY or config.EURI_API_KEY == "your_euriai_api_key_here":
            print("⚠ EURI_API_KEY not set - skipping embedding test")
            print("Please set your API key in .env file to test embeddings")
            return
        
        try:
            generator = EmbeddingGeneratorTool()
            
            # Test single embedding
            test_text = "Stunting prevalence in Kakamega County was 22.3% in 2022"
            embedding = generator.generate_embedding(test_text)
            
            assert isinstance(embedding, list)
            assert len(embedding) > 0
            assert all(isinstance(x, (int, float)) for x in embedding)
            
            print(f"✓ Single embedding generated: {len(embedding)} dimensions")
            
            # Test batch embeddings
            test_texts = [
                "Child health indicators in Western Kenya",
                "Maternal mortality rates by county",
                "Immunization coverage in rural areas"
            ]
            
            embeddings = generator.generate_embeddings_batch(test_texts)
            assert len(embeddings) == len(test_texts)
            assert all(len(emb) == len(embedding) for emb in embeddings)
            
            print(f"✓ Batch embeddings generated: {len(embeddings)} vectors")
            
            # Test indicator embedding
            source = IndicatorSource(document="test.pdf", page_number=1)
            indicator = Indicator(
                name="Stunting prevalence",
                type=IndicatorType.NUTRITION,
                county="Kakamega",
                year=2022,
                value=22.3,
                unit="%",
                source=source
            )
            
            record = generator.create_embedding_record(indicator)
            assert isinstance(record, EmbeddingRecord)
            assert record.county == "Kakamega"
            assert record.year == 2022
            assert len(record.embedding) == len(embedding)
            
            print("✓ Indicator embedding record created")
            
        except Exception as e:
            print(f"✗ Embedding generation failed: {str(e)}")
            print("This might be due to API key issues or network connectivity")
            raise
    
    def test_vector_store(self):
        """Test vector store operations."""
        print("\n=== Testing Vector Store ===")
        
        try:
            store = VectorStoreTool()
            
            # Clear any existing data
            store.clear()
            
            # Create test embedding records
            test_records = []
            for i in range(3):
                record = EmbeddingRecord(
                    id=f"test_{i}",
                    embedding=[0.1 * i] * 384,  # Dummy embedding
                    indicator_id=f"ind_{i}",
                    county=f"County_{i}",
                    year=2022,
                    indicator_type="nutrition",
                    indicator_name=f"Test Indicator {i}",
                    content=f"Test content {i}"
                )
                test_records.append(record)
            
            # Add records
            store.add_records(test_records)
            
            # Check stats
            stats = store.get_stats()
            assert stats["total_records"] == 3
            assert stats["dimension"] == 384
            
            print(f"✓ Vector store created with {stats['total_records']} records")
            print(f"✓ Dimension: {stats['dimension']}")
            
            # Test search
            query_embedding = [0.05] * 384
            results = store.search_by_embedding(query_embedding, top_k=2)
            
            assert len(results) <= 2
            if results:
                assert all(isinstance(r.similarity_score, float) for r in results)
                print(f"✓ Search returned {len(results)} results")
            
            # Clean up
            store.clear()
            print("✓ Vector store cleared")
            
        except Exception as e:
            print(f"✗ Vector store test failed: {str(e)}")
            raise
    
    def test_integration_flow(self):
        """Test basic integration flow."""
        print("\n=== Testing Integration Flow ===")
        
        try:
            # 1. Create sample indicator
            source = IndicatorSource(document="test.pdf", page_number=1)
            indicator = Indicator(
                name="Child mortality rate",
                type=IndicatorType.CHILD_HEALTH,
                county="Nairobi",
                year=2022,
                value=45.2,
                unit="per 1000",
                source=source,
                summary="Child mortality rate in Nairobi was 45.2 per 1000 in 2022"
            )
            
            print("✓ Sample indicator created")
            
            # 2. Generate embedding (if API key available)
            if config.EURI_API_KEY and config.EURI_API_KEY != "your_euriai_api_key_here":
                generator = EmbeddingGeneratorTool()
                record = generator.create_embedding_record(indicator)
                
                # 3. Store in vector store
                store = VectorStoreTool()
                store.clear()
                store.add_records([record])
                
                stats = store.get_stats()
                assert stats["total_records"] == 1
                
                print("✓ End-to-end flow: Indicator → Embedding → Vector Store")
                
                # Clean up
                store.clear()
            else:
                print("⚠ Skipping embedding/vector store integration (no API key)")
            
            print("✓ Integration flow completed successfully")
            
        except Exception as e:
            print(f"✗ Integration flow failed: {str(e)}")
            raise


def run_milestone_test():
    """Run the milestone test manually."""
    print("🚀 DHS RAQ System - Milestone 1 Test")
    print("=" * 50)
    
    test = TestMilestone1()
    
    try:
        test.test_config_loading()
        test.test_data_models()
        test.test_pdf_loading()
        test.test_embedding_generation()
        test.test_vector_store()
        test.test_integration_flow()
        
        print("\n" + "=" * 50)
        print("🎉 MILESTONE 1 PASSED - Core components are working!")
        print("✓ Configuration loading")
        print("✓ Data models")
        print("✓ PDF loading")
        print("✓ Embedding generation")
        print("✓ Vector store operations")
        print("✓ Basic integration flow")
        print("\nReady to proceed to next milestone!")
        
    except Exception as e:
        print("\n" + "=" * 50)
        print(f"❌ MILESTONE 1 FAILED: {str(e)}")
        print("Please fix the issues before proceeding.")
        raise


if __name__ == "__main__":
    run_milestone_test()
