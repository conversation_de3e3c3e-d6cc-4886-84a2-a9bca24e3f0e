"""
Simplified Milestone 1 Test: Core Components Validation

This test validates the core components we've built so far:
1. Configuration and data models
2. PDF loading functionality  
3. Basic embedding and vector store operations
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def test_data_models():
    """Test data model creation and validation."""
    print("\n=== Testing Data Models ===")

    from src.models.indicator import Indicator, IndicatorType, IndicatorSource
    from src.models.county import County, CountyLocation, KENYA_COUNTIES
    from src.models.embedding import EmbeddingRecord

    # Test County model
    location = CountyLocation(
        latitude=0.2827, longitude=34.7519, region="Western")
    county = County(name="Kakamega", location=location)
    assert county.name == "Kakamega"
    assert county.location.region == "Western"
    print("✓ County model works")

    # Test Indicator model
    source = IndicatorSource(document="test.pdf", page_number=1)
    indicator = Indicator(
        name="Stunting prevalence",
        type=IndicatorType.NUTRITION,
        county="Kakamega",
        year=2022,
        value=22.3,
        unit="%",
        source=source
    )
    assert indicator.county == "Kakamega"
    assert indicator.value == 22.3
    print("✓ Indicator model works")

    # Test embedding text generation
    embedding_text = indicator.to_embedding_text()
    assert "Kakamega" in embedding_text
    assert "2022" in embedding_text
    print("✓ Embedding text generation works")

    # Test Kenya counties data
    assert "Kakamega" in KENYA_COUNTIES
    assert len(KENYA_COUNTIES) == 47  # Kenya has 47 counties
    print(f"✓ Kenya counties loaded: {len(KENYA_COUNTIES)} counties")


def test_pdf_loading():
    """Test PDF loading functionality."""
    print("\n=== Testing PDF Loading ===")

    from src.config import config
    from src.tools.pdf_loader import PDFLoaderTool

    # Check if PDFs exist
    pdf_files = list(config.PDF_UPLOAD_PATH.glob("*.pdf"))
    if not pdf_files:
        print("⚠ No PDF files found in uploads folder - skipping PDF test")
        return

    # Test with first PDF (use smaller one for faster testing)
    pdf_files.sort(key=lambda x: x.stat().st_size)  # Sort by size
    pdf_path = pdf_files[0]  # Use smallest PDF
    print(
        f"Testing with: {pdf_path.name} ({pdf_path.stat().st_size / (1024*1024):.1f} MB)")

    loader = PDFLoaderTool()

    try:
        result = loader.load_pdf(str(pdf_path))

        assert result.total_pages > 0
        assert len(result.pages) > 0
        assert result.success_rate > 0

        print(f"✓ PDF loaded: {result.total_pages} pages")
        print(f"✓ Success rate: {result.success_rate:.1%}")
        print(f"✓ Processing time: {result.total_processing_time_ms:.1f}ms")

        # Check page types
        page_types = {}
        for page in result.pages:
            page_type = page.page_type.value
            page_types[page_type] = page_types.get(page_type, 0) + 1

        print(f"✓ Page types: {page_types}")

        # Test page classification (skip if no API key)
        from src.config import config
        if config.EURI_API_KEY and config.EURI_API_KEY != "your_euriai_api_key_here":
            from src.tools.page_classifier import PageClassifierTool
            classifier = PageClassifierTool()

            # Classify first few pages only (to save time)
            test_pages = result.pages[:3]
            classified_pages = classifier.classify_pages_batch(test_pages)

            print(
                f"✓ Page classification completed for {len(classified_pages)} pages")
        else:
            print("⚠ Skipping page classification (no API key set)")

    except Exception as e:
        print(f"✗ PDF loading failed: {str(e)}")
        raise


def test_vector_store():
    """Test vector store operations without API calls."""
    print("\n=== Testing Vector Store ===")

    from src.tools.vector_store import VectorStoreTool
    from src.models.embedding import EmbeddingRecord

    try:
        store = VectorStoreTool()

        # Clear any existing data
        store.clear()

        # Create test embedding records with dummy embeddings
        test_records = []
        for i in range(3):
            record = EmbeddingRecord(
                id=f"test_{i}",
                embedding=[0.1 * (i + 1)] * 384,  # Dummy embedding
                indicator_id=f"ind_{i}",
                county=f"County_{i}",
                year=2022,
                indicator_type="nutrition",
                indicator_name=f"Test Indicator {i}",
                content=f"Test content {i}"
            )
            test_records.append(record)

        # Add records
        store.add_records(test_records)

        # Check stats
        stats = store.get_stats()
        assert stats["total_records"] == 3
        assert stats["dimension"] == 384

        print(f"✓ Vector store created with {stats['total_records']} records")
        print(f"✓ Dimension: {stats['dimension']}")

        # Test search with dummy embedding
        query_embedding = [0.05] * 384
        results = store.search_by_embedding(query_embedding, top_k=2)

        assert len(results) <= 2
        if results:
            assert all(isinstance(r.similarity_score, float) for r in results)
            print(f"✓ Search returned {len(results)} results")

        # Clean up
        store.clear()
        print("✓ Vector store cleared")

    except Exception as e:
        print(f"✗ Vector store test failed: {str(e)}")
        raise


def test_integration_flow():
    """Test basic integration flow without API calls."""
    print("\n=== Testing Integration Flow ===")

    from src.models.indicator import Indicator, IndicatorType, IndicatorSource
    from src.models.embedding import EmbeddingRecord
    from src.tools.vector_store import VectorStoreTool

    try:
        # 1. Create sample indicator
        source = IndicatorSource(document="test.pdf", page_number=1)
        indicator = Indicator(
            name="Child mortality rate",
            type=IndicatorType.CHILD_HEALTH,
            county="Nairobi",
            year=2022,
            value=45.2,
            unit="per 1000",
            source=source,
            summary="Child mortality rate in Nairobi was 45.2 per 1000 in 2022"
        )

        print("✓ Sample indicator created")

        # 2. Create embedding record manually (without API call)
        embedding_text = indicator.to_embedding_text()
        dummy_embedding = [0.1] * 384  # Dummy embedding for testing

        record = EmbeddingRecord(
            id=f"ind_{indicator.county}_{indicator.year}_{hash(indicator.name)}",
            embedding=dummy_embedding,
            indicator_id="test_indicator_1",
            county=indicator.county,
            year=indicator.year,
            indicator_type=indicator.type.value,
            indicator_name=indicator.name,
            content=embedding_text
        )

        print("✓ Embedding record created (dummy embedding)")

        # 3. Store in vector store
        store = VectorStoreTool()
        store.clear()
        store.add_records([record])

        stats = store.get_stats()
        assert stats["total_records"] == 1

        print("✓ End-to-end flow: Indicator → Embedding Record → Vector Store")

        # Clean up
        store.clear()

        print("✓ Integration flow completed successfully")

    except Exception as e:
        print(f"✗ Integration flow failed: {str(e)}")
        raise


def run_milestone_test():
    """Run the simplified milestone test."""
    print("🚀 DHS RAQ System - Milestone 1 Test (Simplified)")
    print("=" * 60)

    try:
        test_data_models()
        test_pdf_loading()
        test_vector_store()
        test_integration_flow()

        print("\n" + "=" * 60)
        print("🎉 MILESTONE 1 PASSED - Core components are working!")
        print("✓ Configuration and data models")
        print("✓ PDF loading and page classification")
        print("✓ Vector store operations")
        print("✓ Basic integration flow")
        print("\n📋 NEXT STEPS:")
        print("1. Set EURI_API_KEY in .env file for embedding generation")
        print("2. Complete remaining tools (text extraction, table extraction)")
        print("3. Build query processing and visualization components")
        print("4. Create Streamlit UI")
        print("\nReady to proceed to next milestone!")

        return True

    except Exception as e:
        print("\n" + "=" * 60)
        print(f"❌ MILESTONE 1 FAILED: {str(e)}")
        print("Please fix the issues before proceeding.")
        return False


if __name__ == "__main__":
    success = run_milestone_test()
    sys.exit(0 if success else 1)
