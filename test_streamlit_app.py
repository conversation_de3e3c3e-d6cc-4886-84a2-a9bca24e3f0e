"""Test the Streamlit app functionality."""

import sys
from pathlib import Path
import subprocess
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def test_streamlit_imports():
    """Test that all imports work correctly."""
    print("🧪 Testing Streamlit App Imports")
    print("=" * 40)

    try:
        # Test basic imports
        import streamlit as st
        import pandas as pd
        import plotly.express as px
        print("✓ Streamlit and visualization libraries imported")

        # Test our custom imports
        from src.config import config
        from src.tools.pdf_loader import PDFLoaderTool
        from src.tools.text_agent import TextAgentTool
        from src.tools.embedding_generator import EmbeddingGeneratorTool
        from src.tools.vector_store import VectorStoreTool
        from src.tools.query_agent import QueryAgentTool
        from src.models.query import QueryRequest
        print("✓ All custom modules imported successfully")

        # Test configuration
        print(f"✓ App name: {config.APP_NAME}")
        print(f"✓ Upload path: {config.PDF_UPLOAD_PATH}")
        print(
            f"✓ API key configured: {'Yes' if config.EURI_API_KEY and config.EURI_API_KEY != 'your_euriai_api_key_here' else 'No'}")

        return True

    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False


def test_app_syntax():
    """Test that the app.py file has valid syntax."""
    print("\n🔍 Testing App Syntax")
    print("=" * 40)

    try:
        # Try to compile the app.py file
        with open("app.py", "r", encoding="utf-8") as f:
            code = f.read()

        compile(code, "app.py", "exec")
        print("✓ app.py syntax is valid")
        return True

    except SyntaxError as e:
        print(f"❌ Syntax error in app.py: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Error checking syntax: {str(e)}")
        return False


def test_streamlit_run():
    """Test that Streamlit can start the app (quick check)."""
    print("\n🚀 Testing Streamlit Launch")
    print("=" * 40)

    try:
        # Try to run streamlit with --help to verify it works
        result = subprocess.run(
            ["streamlit", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )

        if result.returncode == 0:
            print("✓ Streamlit command available")
        else:
            print("❌ Streamlit command failed")
            return False

        # Check if we can validate the app without running it
        result = subprocess.run(
            ["python", "-m", "streamlit", "run", "app.py", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )

        print("✓ Streamlit can recognize the app file")
        return True

    except subprocess.TimeoutExpired:
        print("⚠️ Streamlit test timed out (this is normal)")
        return True
    except Exception as e:
        print(f"❌ Streamlit test failed: {str(e)}")
        return False


def show_app_info():
    """Show information about how to run the app."""
    print("\n📋 How to Run the DHS RAQ System")
    print("=" * 50)
    print("""
🚀 To start the Streamlit application:

1. Open a terminal/command prompt
2. Navigate to the project directory:
   cd c:\\Euron\\kdhs

3. Run the Streamlit app:
   streamlit run app.py

4. The app will open in your browser at:
   http://localhost:8501

📋 App Features:
• 🔍 Query Interface - Ask natural language questions
• 📄 PDF Processing - Upload and process DHS documents  
• 📊 Data Explorer - Browse extracted indicators
• ℹ️ About - System information and documentation

⚙️ Before using:
• Ensure your EURI_API_KEY is set in the .env file
• Upload some PDF documents for processing
• Process the PDFs to build the knowledge base
• Start asking questions!

🎯 Example queries to try:
• "What is the stunting prevalence in Western Kenya?"
• "Compare immunization coverage between counties"
• "Show me child health indicators for 2022"
""")


def main():
    """Run all tests."""
    print("🏥 DHS RAQ System - Streamlit App Test")
    print("=" * 50)

    success = True
    success &= test_streamlit_imports()
    success &= test_app_syntax()
    success &= test_streamlit_run()

    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Streamlit app is ready to run")
        show_app_info()
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Please fix the issues before running the app")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
