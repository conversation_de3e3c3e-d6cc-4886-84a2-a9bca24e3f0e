"""Main RAQ Agent for orchestrating the complete extraction and processing pipeline."""

import time
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
from pathlib import Path

from ..models.extraction import ExtractionResult, PDFPage
from ..models.indicator import Indicator
from ..models.query import QueryRequest, QueryResponse
from ..tools.pdf_loader import PDFLoaderTool
from ..tools.page_classifier import PageClassifierTool
from ..tools.text_agent import TextAgentTool
from ..tools.table_extractor import TableExtractorTool
from ..tools.vision_agent import VisionAgentTool
from ..tools.embedding_generator import EmbeddingGeneratorTool
from ..tools.vector_store import VectorStoreTool
from ..tools.query_agent import QueryAgentTool
from ..tools.indicator_merger import IndicatorMerger
from ..tools.chart_renderer import ChartRendererTool
from ..config import config

logger = logging.getLogger(__name__)


class RAQAgent:
    """
    Main Retrieval-Augmented Questioning Agent.

    Orchestrates the complete pipeline from PDF processing to query answering:
    1. PDF loading and page classification
    2. Multi-modal content extraction (text, tables, images)
    3. Indicator normalization and merging
    4. Vector embedding and storage
    5. Natural language query processing
    6. Response generation with visualizations
    """

    def __init__(self):
        """Initialize the RAQ agent with all required tools."""
        logger.info("Initializing RAQ Agent with complete pipeline")

        # Initialize all tools
        self.pdf_loader = PDFLoaderTool()
        self.page_classifier = PageClassifierTool()
        self.text_agent = TextAgentTool()
        self.table_extractor = TableExtractorTool()
        self.vision_agent = VisionAgentTool()
        self.embedding_generator = EmbeddingGeneratorTool()
        self.vector_store = VectorStoreTool()
        self.query_agent = QueryAgentTool()
        self.indicator_merger = IndicatorMerger()
        self.chart_renderer = ChartRendererTool()

        # Pipeline statistics
        self.pipeline_stats = {
            "documents_processed": 0,
            "total_pages_processed": 0,
            "total_indicators_extracted": 0,
            "total_queries_processed": 0,
            "last_processing_time": None
        }

        logger.info("RAQ Agent initialized successfully")

    def process_document(self, document_path: str, year: int = 2022,
                         max_pages: Optional[int] = None) -> Dict[str, Any]:
        """
        Process a complete DHS document through the full extraction pipeline.

        Args:
            document_path: Path to the PDF document
            year: Survey year for the document
            max_pages: Maximum number of pages to process (for testing)

        Returns:
            Dictionary with processing results and statistics
        """
        start_time = time.time()
        document_name = Path(document_path).name

        logger.info(f"Starting complete document processing: {document_name}")

        try:
            # Step 1: Load and classify PDF pages
            logger.info("Step 1: Loading and classifying PDF pages")
            pdf_result = self.pdf_loader.load_pdf(document_path)

            if not pdf_result.pages:
                return self._create_error_result("No pages found in PDF")

            # Limit pages if specified
            pages_to_process = pdf_result.pages[:
                                                max_pages] if max_pages else pdf_result.pages
            logger.info(f"Processing {len(pages_to_process)} pages")

            # Step 2: Extract indicators from all sources
            logger.info("Step 2: Multi-modal indicator extraction")
            all_indicators = []

            # Extract from text content
            text_indicators = self._extract_text_indicators(
                pages_to_process, document_name, year)
            all_indicators.extend(text_indicators)
            logger.info(
                f"Extracted {len(text_indicators)} indicators from text")

            # Extract from tables
            table_indicators = self._extract_table_indicators(
                pages_to_process, document_name, year)
            all_indicators.extend(table_indicators)
            logger.info(
                f"Extracted {len(table_indicators)} indicators from tables")

            # Extract from images/charts
            image_indicators = self._extract_image_indicators(
                pages_to_process, document_name, year)
            all_indicators.extend(image_indicators)
            logger.info(
                f"Extracted {len(image_indicators)} indicators from images")

            # Step 3: Merge and normalize indicators
            logger.info("Step 3: Merging and normalizing indicators")
            merged_indicators = self.indicator_merger.merge_indicators(
                [text_indicators, table_indicators, image_indicators],
                merge_strategy="comprehensive"
            )
            logger.info(
                f"Merged to {len(merged_indicators)} unique indicators")

            # Step 4: Generate embeddings and store in vector database
            logger.info(
                "Step 4: Generating embeddings and storing in vector database")
            if merged_indicators:
                embedding_records = self.embedding_generator.create_embedding_records_batch(
                    merged_indicators)
                self.vector_store.add_records(embedding_records)
                logger.info(
                    f"Stored {len(embedding_records)} embedding records")

            # Step 5: Update pipeline statistics
            processing_time = time.time() - start_time
            self._update_pipeline_stats(
                pdf_result, merged_indicators, processing_time)

            # Create result summary
            result = {
                "success": True,
                "document_name": document_name,
                "processing_time_seconds": processing_time,
                "pages_processed": len(pages_to_process),
                "total_pages": len(pdf_result.pages),
                "indicators_extracted": {
                    "text": len(text_indicators),
                    "tables": len(table_indicators),
                    "images": len(image_indicators),
                    "total_raw": len(all_indicators),
                    "merged_final": len(merged_indicators)
                },
                "merge_statistics": self.indicator_merger.get_merge_statistics(
                    len(all_indicators), len(merged_indicators)
                ),
                "vector_store_stats": self.vector_store.get_stats(),
                "pipeline_stats": self.pipeline_stats.copy()
            }

            logger.info(
                f"Document processing completed successfully in {processing_time:.2f} seconds")
            return result

        except Exception as e:
            logger.error(f"Document processing failed: {str(e)}")
            return self._create_error_result(f"Processing failed: {str(e)}")

    def _extract_text_indicators(self, pages: List[PDFPage], document_name: str, year: int) -> List[Indicator]:
        """Extract indicators from text content."""
        text_indicators = []

        for page in pages:
            if page.page_type.value in ['text_heavy', 'mixed']:
                try:
                    indicators = self.text_agent.extract_indicators(
                        page, document_name)
                    text_indicators.extend(indicators)
                except Exception as e:
                    logger.warning(
                        f"Failed to extract text indicators from page {page.page_number}: {str(e)}")
                    continue

        return text_indicators

    def _extract_table_indicators(self, pages: List[PDFPage], document_name: str, year: int) -> List[Indicator]:
        """Extract indicators from tables."""
        table_indicators = []

        for page in pages:
            if page.page_type.value in ['table_heavy', 'mixed']:
                try:
                    # Extract tables from page
                    tables = self.table_extractor.extract_tables_from_page(
                        page, document_name)

                    # Extract indicators from each table
                    for table in tables:
                        indicators = self.table_extractor.extract_indicators_from_table(
                            table, document_name, year
                        )
                        table_indicators.extend(indicators)

                except Exception as e:
                    logger.warning(
                        f"Failed to extract table indicators from page {page.page_number}: {str(e)}")
                    continue

        return table_indicators

    def _extract_image_indicators(self, pages: List[PDFPage], document_name: str, year: int) -> List[Indicator]:
        """Extract indicators from images and charts."""
        image_indicators = []

        for page in pages:
            if page.page_type.value in ['image_heavy', 'mixed']:
                try:
                    # Extract images from page
                    images = self.vision_agent.extract_images_from_page(
                        page, document_name)

                    # Analyze each image for indicators
                    for image in images:
                        indicators = self.vision_agent.analyze_image_content(
                            image, document_name, year
                        )
                        image_indicators.extend(indicators)

                except Exception as e:
                    logger.warning(
                        f"Failed to extract image indicators from page {page.page_number}: {str(e)}")
                    continue

        return image_indicators

    def process_query(self, query_request: QueryRequest) -> QueryResponse:
        """
        Process a natural language query using the complete RAQ pipeline.

        Args:
            query_request: Query request object

        Returns:
            Complete query response with indicators, insights, and visualizations
        """
        start_time = time.time()

        logger.info(f"Processing query: {query_request.query}")

        try:
            # Use the query agent for core processing
            response = self.query_agent.process_query(query_request)

            # Enhance response with additional visualizations if requested
            if query_request.include_charts and response.indicators:
                try:
                    # Create additional chart types
                    chart_recommendations = self.chart_renderer.get_chart_recommendations(
                        response.indicators)

                    if len(chart_recommendations) > 1:
                        # Create multiple charts
                        additional_charts = self.chart_renderer.create_multiple_charts(
                            # Take 2 additional types
                            response.indicators, chart_recommendations[1:3]
                        )

                        # Store additional charts in response (would need to extend QueryResponse model)
                        logger.info(
                            f"Created {len(additional_charts)} additional charts")

                except Exception as e:
                    logger.warning(
                        f"Failed to create additional charts: {str(e)}")

            # Update query statistics
            self.pipeline_stats["total_queries_processed"] += 1

            logger.info(
                f"Query processed successfully in {response.processing_time_ms:.1f}ms")
            return response

        except Exception as e:
            logger.error(f"Query processing failed: {str(e)}")

            # Return error response
            return QueryResponse(
                query=query_request.query,
                indicators=[],
                total_indicators=0,
                summary=f"Query processing failed: {str(e)}",
                insights=[],
                comparisons=[],
                chart_data=None,
                confidence_score=0.0,
                processing_time_ms=time.time() - start_time * 1000
            )

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status and statistics."""
        vector_stats = self.vector_store.get_stats()

        return {
            "system_status": "operational",
            "pipeline_stats": self.pipeline_stats.copy(),
            "vector_store": vector_stats,
            "tools_status": {
                "pdf_loader": "ready",
                "page_classifier": "ready",
                "text_agent": "ready",
                "table_extractor": "ready",
                "vision_agent": "ready",
                "embedding_generator": "ready",
                "vector_store": "ready",
                "query_agent": "ready",
                "indicator_merger": "ready",
                "chart_renderer": "ready"
            },
            "last_updated": datetime.now().isoformat()
        }

    def clear_system(self) -> Dict[str, Any]:
        """Clear all processed data and reset the system."""
        logger.info("Clearing RAQ system data")

        try:
            # Clear vector store
            self.vector_store.clear()

            # Reset pipeline statistics
            self.pipeline_stats = {
                "documents_processed": 0,
                "total_pages_processed": 0,
                "total_indicators_extracted": 0,
                "total_queries_processed": 0,
                "last_processing_time": None
            }

            logger.info("System cleared successfully")
            return {"success": True, "message": "System cleared successfully"}

        except Exception as e:
            logger.error(f"Failed to clear system: {str(e)}")
            return {"success": False, "error": str(e)}

    def _update_pipeline_stats(self, pdf_result: ExtractionResult, indicators: List[Indicator], processing_time: float):
        """Update pipeline statistics."""
        self.pipeline_stats["documents_processed"] += 1
        self.pipeline_stats["total_pages_processed"] += len(pdf_result.pages)
        self.pipeline_stats["total_indicators_extracted"] += len(indicators)
        self.pipeline_stats["last_processing_time"] = processing_time

    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """Create an error result dictionary."""
        return {
            "success": False,
            "error": error_message,
            "document_name": None,
            "processing_time_seconds": 0,
            "pages_processed": 0,
            "indicators_extracted": {
                "text": 0,
                "tables": 0,
                "images": 0,
                "total_raw": 0,
                "merged_final": 0
            }
        }

    def batch_process_documents(self, document_paths: List[str], year: int = 2022) -> List[Dict[str, Any]]:
        """
        Process multiple documents in batch.

        Args:
            document_paths: List of paths to PDF documents
            year: Survey year for the documents

        Returns:
            List of processing results for each document
        """
        logger.info(
            f"Starting batch processing of {len(document_paths)} documents")

        results = []
        for i, doc_path in enumerate(document_paths, 1):
            logger.info(
                f"Processing document {i}/{len(document_paths)}: {Path(doc_path).name}")

            try:
                result = self.process_document(doc_path, year)
                results.append(result)
            except Exception as e:
                logger.error(
                    f"Failed to process document {doc_path}: {str(e)}")
                results.append(self._create_error_result(
                    f"Batch processing failed: {str(e)}"))

        logger.info(
            f"Batch processing completed. {sum(1 for r in results if r['success'])} successful, {sum(1 for r in results if not r['success'])} failed")
        return results
