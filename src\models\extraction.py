"""PDF extraction and processing data models."""

from enum import Enum
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from .indicator import Indicator


class PageType(str, Enum):
    """Types of PDF pages based on content."""
    TEXT_HEAVY = "text_heavy"
    TABLE_HEAVY = "table_heavy"
    IMAGE_HEAVY = "image_heavy"
    MIXED = "mixed"
    COVER = "cover"
    TOC = "table_of_contents"
    APPENDIX = "appendix"
    UNKNOWN = "unknown"


class PDFPage(BaseModel):
    """Represents a single page from a PDF."""
    
    page_number: int = Field(..., description="Page number (1-based)")
    page_type: PageType = Field(..., description="Classified page type")
    
    # Content
    text_content: Optional[str] = Field(None, description="Extracted text content")
    image_path: Optional[str] = Field(None, description="Path to extracted page image")
    
    # Metadata
    width: Optional[float] = Field(None, description="Page width")
    height: Optional[float] = Field(None, description="Page height")
    
    # Processing information
    extraction_method: str = Field(..., description="Method used for extraction")
    confidence_score: Optional[float] = Field(None, description="Extraction confidence")
    processing_time_ms: Optional[float] = Field(None, description="Processing time")
    
    # Detected elements
    tables_detected: int = Field(default=0, description="Number of tables detected")
    images_detected: int = Field(default=0, description="Number of images detected")
    charts_detected: int = Field(default=0, description="Number of charts detected")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()


class TableExtraction(BaseModel):
    """Extracted table information."""
    
    table_id: str = Field(..., description="Unique table identifier")
    title: Optional[str] = Field(None, description="Table title")
    headers: List[str] = Field(..., description="Column headers")
    rows: List[List[str]] = Field(..., description="Table rows")
    footnotes: List[str] = Field(default_factory=list, description="Table footnotes")
    
    # Metadata
    page_number: int = Field(..., description="Source page number")
    position: Optional[Dict[str, float]] = Field(None, description="Table position on page")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()


class ImageExtraction(BaseModel):
    """Extracted image/chart information."""
    
    image_id: str = Field(..., description="Unique image identifier")
    image_type: str = Field(..., description="Type of image (chart, map, photo, etc.)")
    image_path: str = Field(..., description="Path to extracted image file")
    
    # Content analysis
    caption: Optional[str] = Field(None, description="Image caption")
    description: Optional[str] = Field(None, description="AI-generated description")
    detected_text: Optional[str] = Field(None, description="OCR extracted text")
    
    # Metadata
    page_number: int = Field(..., description="Source page number")
    position: Optional[Dict[str, float]] = Field(None, description="Image position on page")
    dimensions: Optional[Dict[str, int]] = Field(None, description="Image dimensions")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()


class ExtractionResult(BaseModel):
    """Complete extraction result for a PDF."""
    
    # Source information
    pdf_path: str = Field(..., description="Path to source PDF")
    pdf_name: str = Field(..., description="PDF filename")
    total_pages: int = Field(..., description="Total number of pages")
    
    # Extracted content
    pages: List[PDFPage] = Field(..., description="Extracted pages")
    tables: List[TableExtraction] = Field(default_factory=list, description="Extracted tables")
    images: List[ImageExtraction] = Field(default_factory=list, description="Extracted images")
    indicators: List[Indicator] = Field(default_factory=list, description="Extracted indicators")
    
    # Processing metadata
    extraction_started: datetime = Field(..., description="Extraction start time")
    extraction_completed: Optional[datetime] = Field(None, description="Extraction completion time")
    total_processing_time_ms: Optional[float] = Field(None, description="Total processing time")
    
    # Quality metrics
    success_rate: Optional[float] = Field(None, description="Overall extraction success rate")
    error_count: int = Field(default=0, description="Number of errors encountered")
    warning_count: int = Field(default=0, description="Number of warnings")
    
    # Errors and warnings
    errors: List[str] = Field(default_factory=list, description="Error messages")
    warnings: List[str] = Field(default_factory=list, description="Warning messages")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "pdf_path": self.pdf_path,
            "pdf_name": self.pdf_name,
            "total_pages": self.total_pages,
            "pages": [page.to_dict() for page in self.pages],
            "tables": [table.to_dict() for table in self.tables],
            "images": [image.to_dict() for image in self.images],
            "indicators": [ind.to_dict() for ind in self.indicators],
            "extraction_started": self.extraction_started.isoformat(),
            "extraction_completed": self.extraction_completed.isoformat() if self.extraction_completed else None,
            "total_processing_time_ms": self.total_processing_time_ms,
            "success_rate": self.success_rate,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "errors": self.errors,
            "warnings": self.warnings
        }
    
    def get_pages_by_type(self, page_type: PageType) -> List[PDFPage]:
        """Get pages filtered by type."""
        return [page for page in self.pages if page.page_type == page_type]
    
    def get_indicators_by_county(self, county: str) -> List[Indicator]:
        """Get indicators filtered by county."""
        return [ind for ind in self.indicators if ind.county.lower() == county.lower()]
    
    def get_indicators_by_year(self, year: int) -> List[Indicator]:
        """Get indicators filtered by year."""
        return [ind for ind in self.indicators if ind.year == year]
