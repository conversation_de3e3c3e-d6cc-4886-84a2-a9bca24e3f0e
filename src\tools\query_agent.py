"""Advanced query processing agent with natural language understanding and context retrieval."""

import re
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging

from euriai import EuriaiClient
from ..models.query import QueryRequest, QueryResponse, ComparisonResult
from ..models.indicator import Indicator, IndicatorType
from ..models.embedding import VectorSearchRequest, QueryResult
from ..models.county import KENYA_COUNTIES
from ..config import config
from .embedding_generator import EmbeddingGeneratorTool
from .vector_store import VectorStoreTool

logger = logging.getLogger(__name__)


class QueryAgentTool:
    """Advanced agent for processing natural language queries and generating comprehensive responses."""

    def __init__(self):
        """Initialize the query agent with all necessary components."""
        self.client = EuriaiClient(
            api_key=config.EURI_API_KEY,
            model=config.DEFAULT_LLM_MODEL
        )

        self.embedding_generator = EmbeddingGeneratorTool()
        self.vector_store = VectorStoreTool()

        # County names for validation
        self.county_names = list(KENYA_COUNTIES.keys())

        # Advanced query understanding prompt
        self.query_analysis_prompt = """
You are an expert analyst for Kenya's Demographic and Health Survey (DHS) data. Your task is to understand natural language queries about health indicators and extract precise search parameters.

## QUERY ANALYSIS GUIDELINES:

### 1. INDICATOR IDENTIFICATION
Identify what health indicators the user is asking about:
- **Child Health**: stunting, wasting, underweight, child mortality, neonatal mortality
- **Maternal Health**: maternal mortality, antenatal care, skilled birth attendance, postnatal care  
- **Nutrition**: malnutrition, anemia, vitamin deficiency, breastfeeding practices
- **Immunization**: vaccination coverage (BCG, DPT, polio, measles, pentavalent)
- **Family Planning**: contraceptive use, unmet need, birth spacing
- **HIV/AIDS**: HIV prevalence, testing, treatment, prevention
- **Malaria**: prevalence, treatment, prevention (ITN use, IRS)
- **Water/Sanitation**: access to improved water, sanitation facilities
- **Demographics**: fertility rates, population characteristics
- **Socioeconomic**: education, wealth quintiles, employment

### 2. GEOGRAPHIC SCOPE
Extract county/region information:
Valid Kenya counties: {counties}
- Handle variations like "Western Kenya" (Kakamega, Vihiga, Bungoma, Busia)
- "Central Kenya", "Coast", "Northern Kenya", etc.
- Specific county names

### 3. TEMPORAL SCOPE  
Identify time periods:
- Specific years (2022, 2014, 2008-09, 2003)
- Trends ("over time", "changes since", "compared to previous")
- Latest data ("current", "recent", "2022")

### 4. COMPARISON INTENT
Detect comparison requests:
- County vs county comparisons
- Time period comparisons  
- Indicator comparisons
- Regional comparisons

### 5. OUTPUT REQUIREMENTS
Determine what format user wants:
- Tables, charts, maps, summaries
- Specific metrics (percentages, counts, rates)
- Detailed vs overview responses

## USER QUERY:
{query}

## OUTPUT FORMAT:
Return a JSON object with query analysis:

```json
{{
    "query_intent": {{
        "primary_intent": "comparison/trend_analysis/specific_lookup/overview",
        "indicators_requested": ["stunting", "immunization", "maternal_mortality"],
        "counties_requested": ["Kakamega", "Vihiga"],
        "years_requested": [2022, 2014],
        "comparison_type": "county_comparison/time_comparison/indicator_comparison/none",
        "output_format": "table/chart/map/summary/comprehensive"
    }},
    "search_strategy": {{
        "primary_search_terms": ["stunting prevalence", "immunization coverage"],
        "secondary_search_terms": ["child health", "vaccination"],
        "geographic_filters": ["Kakamega", "Vihiga"],
        "temporal_filters": [2022],
        "indicator_type_filters": ["child_health", "immunization"]
    }},
    "response_requirements": {{
        "include_tables": true,
        "include_charts": true,
        "include_maps": false,
        "include_summary": true,
        "detail_level": "comprehensive/moderate/brief",
        "comparison_analysis": true
    }},
    "confidence_assessment": {{
        "query_clarity": "high/medium/low",
        "geographic_specificity": "high/medium/low", 
        "indicator_specificity": "high/medium/low",
        "overall_confidence": "high/medium/low"
    }}
}}
```

## CRITICAL REQUIREMENTS:
1. Extract ALL relevant indicators mentioned or implied
2. Validate county names against the provided list
3. Infer reasonable defaults for missing information
4. Assess query clarity and confidence levels
5. Provide comprehensive search strategy

Analyze the query:
"""

        # Response generation prompt
        self.response_generation_prompt = """
You are an expert health data analyst generating comprehensive responses to queries about Kenya's DHS data.

## CONTEXT:
Query: {original_query}
Retrieved Indicators: {indicators_summary}
Analysis Intent: {query_intent}

## RETRIEVED DATA:
{indicators_data}

## RESPONSE GENERATION GUIDELINES:

### 1. STRUCTURE YOUR RESPONSE
- **Executive Summary**: 2-3 sentence overview of key findings
- **Detailed Analysis**: Specific indicator values and comparisons
- **Key Insights**: Notable patterns, trends, or disparities
- **Data Context**: Survey year, methodology notes if relevant

### 2. COMPARISON ANALYSIS
When comparing data:
- Highlight significant differences (>5% for percentages)
- Provide context for differences (geographic, socioeconomic factors)
- Note data quality or sample size considerations
- Suggest potential explanations for patterns

### 3. STATISTICAL INTERPRETATION
- Present percentages with appropriate precision (1 decimal place)
- Include confidence intervals when available
- Note sample sizes when provided
- Explain statistical significance of differences

### 4. ACTIONABLE INSIGHTS
- Identify counties/regions performing well or poorly
- Suggest areas needing intervention
- Highlight successful practices that could be replicated
- Note data gaps or limitations

## OUTPUT FORMAT:
Generate a comprehensive response with:

```json
{{
    "executive_summary": "2-3 sentence overview of key findings",
    "detailed_analysis": {{
        "primary_findings": [
            "Finding 1 with specific data",
            "Finding 2 with specific data"
        ],
        "comparisons": [
            "Comparison 1 between counties/indicators",
            "Comparison 2 with statistical context"
        ]
    }},
    "key_insights": [
        "Insight 1 about patterns or trends",
        "Insight 2 about disparities or successes"
    ],
    "recommendations": [
        "Actionable recommendation 1",
        "Actionable recommendation 2"
    ],
    "data_context": {{
        "survey_year": 2022,
        "counties_covered": ["County1", "County2"],
        "indicators_analyzed": ["Indicator1", "Indicator2"],
        "data_quality_notes": "Any relevant methodology or limitation notes"
    }}
}}
```

Generate the response:
"""

    def process_query(self, request: QueryRequest) -> QueryResponse:
        """
        Process a natural language query and generate comprehensive response.

        Args:
            request: QueryRequest with user query and preferences

        Returns:
            QueryResponse with analysis, data, and insights
        """
        start_time = datetime.now()

        try:
            logger.info(f"Processing query: {request.query}")

            # Step 1: Analyze query intent and extract parameters
            query_analysis = self._analyze_query_intent(request.query)

            # Step 2: Generate search embeddings and retrieve relevant indicators
            search_results = self._retrieve_relevant_indicators(
                query_analysis, request)

            # Step 3: Filter and organize retrieved indicators
            filtered_indicators = self._filter_and_organize_indicators(
                search_results, query_analysis)

            # Step 4: Perform comparison analysis if requested
            comparisons = self._generate_comparisons(
                filtered_indicators, query_analysis)

            # Step 5: Generate comprehensive response
            response_content = self._generate_response_content(
                request.query, query_analysis, filtered_indicators, comparisons
            )

            # Step 6: Prepare visualization data
            chart_data, map_data = self._prepare_visualization_data(
                filtered_indicators, request)

            # Calculate processing time
            processing_time = (
                datetime.now() - start_time).total_seconds() * 1000

            # Build response
            response = QueryResponse(
                original_query=request.query,
                processed_query=query_analysis.get('processed_query'),
                indicators=filtered_indicators,
                total_indicators=len(filtered_indicators),
                comparisons=comparisons,
                summary=response_content.get('executive_summary'),
                insights=response_content.get('key_insights', []),
                chart_data=chart_data if request.include_charts else None,
                map_data=map_data if request.include_maps else None,
                search_results=[],  # Will be populated with search results
                processing_time_ms=processing_time,
                confidence_score=self._parse_confidence_score(
                    query_analysis.get('confidence_assessment', {}).get('overall_confidence', 0.8))
            )

            logger.info(
                f"Query processed successfully in {processing_time:.1f}ms")
            return response

        except Exception as e:
            logger.error(f"Failed to process query: {str(e)}")
            # Return error response
            return QueryResponse(
                original_query=request.query,
                indicators=[],
                total_indicators=0,
                summary=f"Error processing query: {str(e)}",
                processing_time_ms=(
                    datetime.now() - start_time).total_seconds() * 1000,
                confidence_score=0.0
            )

    def _analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """
        Analyze query intent using advanced NLP.

        Args:
            query: Natural language query

        Returns:
            Dictionary with query analysis results
        """
        try:
            prompt = self.query_analysis_prompt.format(
                query=query,
                counties=", ".join(self.county_names)
            )

            response = self.client.generate_completion(
                prompt=prompt,
                temperature=0.1,
                max_tokens=1500
            )

            # Extract content from response
            if isinstance(response, dict) and 'choices' in response:
                content = response['choices'][0]['message']['content']
            else:
                content = response

            # Parse JSON response with better error handling
            json_str = self._extract_json_from_response(content)
            if json_str:
                try:
                    analysis = json.loads(json_str)
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON decode error: {str(e)}")
                    # Try to fix common JSON issues
                    fixed_json = self._fix_json_format(json_str)
                    try:
                        analysis = json.loads(fixed_json)
                    except json.JSONDecodeError:
                        logger.warning("Failed to fix JSON, using fallback")
                        return self._fallback_query_analysis(query)
                logger.info(
                    f"Query analysis completed: {analysis.get('query_intent', {}).get('primary_intent')}")
                return analysis
            else:
                logger.warning("Failed to parse query analysis JSON")
                return self._fallback_query_analysis(query)

        except Exception as e:
            logger.error(f"Query analysis failed: {str(e)}")
            return self._fallback_query_analysis(query)

    def _fallback_query_analysis(self, query: str) -> Dict[str, Any]:
        """Fallback query analysis using simple pattern matching."""
        query_lower = query.lower()

        # Simple pattern matching for fallback
        counties = [
            county for county in self.county_names if county.lower() in query_lower]

        indicators = []
        if any(term in query_lower for term in ['stunt', 'malnutrition', 'underweight']):
            indicators.append('nutrition')
        if any(term in query_lower for term in ['immuniz', 'vaccin']):
            indicators.append('immunization')
        if any(term in query_lower for term in ['maternal', 'antenatal', 'birth']):
            indicators.append('maternal_health')

        return {
            "query_intent": {
                "primary_intent": "specific_lookup",
                "indicators_requested": indicators,
                "counties_requested": counties,
                "years_requested": [2022],
                "comparison_type": "county_comparison" if len(counties) > 1 else "none",
                "output_format": "comprehensive"
            },
            "search_strategy": {
                "primary_search_terms": indicators,
                "geographic_filters": counties,
                "temporal_filters": [2022],
                "indicator_type_filters": indicators
            },
            "confidence_assessment": {
                "overall_confidence": 0.7
            }
        }

    def _retrieve_relevant_indicators(self, query_analysis: Dict[str, Any], request: QueryRequest) -> List[QueryResult]:
        """
        Retrieve relevant indicators using vector similarity search.

        Args:
            query_analysis: Analyzed query intent and parameters
            request: Original query request

        Returns:
            List of relevant query results
        """
        try:
            search_strategy = query_analysis.get('search_strategy', {})

            # Generate search embeddings for primary terms
            primary_terms = search_strategy.get('primary_search_terms', [])
            if not primary_terms:
                primary_terms = [request.query]

            all_results = []

            for term in primary_terms:
                # Generate embedding for search term
                search_embedding = self.embedding_generator.generate_embedding(
                    term)

                # Prepare filters
                filters = {}
                if search_strategy.get('geographic_filters'):
                    filters['counties'] = search_strategy['geographic_filters']
                if search_strategy.get('temporal_filters'):
                    filters['years'] = search_strategy['temporal_filters']
                if search_strategy.get('indicator_type_filters'):
                    filters['indicator_types'] = search_strategy['indicator_type_filters']

                # Search vector store
                results = self.vector_store.search_by_embedding(
                    search_embedding,
                    top_k=request.max_results // len(primary_terms),
                    filters=filters
                )

                all_results.extend(results)

            # Remove duplicates and sort by similarity
            unique_results = {}
            for result in all_results:
                if result.record.id not in unique_results:
                    unique_results[result.record.id] = result
                elif result.similarity_score > unique_results[result.record.id].similarity_score:
                    unique_results[result.record.id] = result

            sorted_results = sorted(unique_results.values(
            ), key=lambda x: x.similarity_score, reverse=True)

            logger.info(f"Retrieved {len(sorted_results)} relevant indicators")
            return sorted_results[:request.max_results]

        except Exception as e:
            logger.error(f"Failed to retrieve indicators: {str(e)}")
            return []

    def _filter_and_organize_indicators(self, search_results: List[QueryResult], query_analysis: Dict[str, Any]) -> List[Indicator]:
        """
        Filter and organize retrieved indicators based on query intent.

        Args:
            search_results: Raw search results from vector store
            query_analysis: Analyzed query parameters

        Returns:
            List of organized Indicator objects
        """
        indicators = []

        for result in search_results:
            # Convert embedding record back to indicator
            record = result.record

            # Create indicator from embedding record metadata
            try:
                indicator = Indicator(
                    id=record.indicator_id,
                    name=record.indicator_name,
                    type=IndicatorType(record.indicator_type),
                    county=record.county,
                    year=record.year,
                    value=record.metadata.get('value'),
                    unit=record.metadata.get('unit'),
                    age_group=record.metadata.get('age_group'),
                    gender=record.metadata.get('gender'),
                    urban_rural=record.metadata.get('urban_rural'),
                    source=None,  # Will need to reconstruct if needed
                    extraction_method=record.metadata.get('extraction_method'),
                    extraction_confidence=record.metadata.get(
                        'extraction_confidence'),
                    summary=record.content
                )
                indicators.append(indicator)

            except Exception as e:
                logger.warning(
                    f"Failed to convert search result to indicator: {str(e)}")
                continue

        # Apply additional filtering based on query intent
        query_intent = query_analysis.get('query_intent', {})

        # Filter by counties if specified
        if query_intent.get('counties_requested'):
            indicators = [
                ind for ind in indicators if ind.county in query_intent['counties_requested']]

        # Filter by years if specified
        if query_intent.get('years_requested'):
            indicators = [
                ind for ind in indicators if ind.year in query_intent['years_requested']]

        # Filter by indicator types if specified
        if query_intent.get('indicators_requested'):
            requested_types = query_intent['indicators_requested']
            indicators = [ind for ind in indicators if any(
                req_type in ind.type.value for req_type in requested_types)]

        logger.info(f"Filtered to {len(indicators)} relevant indicators")
        return indicators

    def _generate_comparisons(self, indicators: List[Indicator], query_analysis: Dict[str, Any]) -> List[ComparisonResult]:
        """
        Generate comparison analysis based on query intent.

        Args:
            indicators: Filtered indicators
            query_analysis: Query analysis results

        Returns:
            List of comparison results
        """
        comparisons = []
        query_intent = query_analysis.get('query_intent', {})
        comparison_type = query_intent.get('comparison_type', 'none')

        if comparison_type == 'none' or len(indicators) < 2:
            return comparisons

        try:
            if comparison_type == 'county_comparison':
                comparisons = self._generate_county_comparisons(indicators)
            elif comparison_type == 'time_comparison':
                comparisons = self._generate_time_comparisons(indicators)
            elif comparison_type == 'indicator_comparison':
                comparisons = self._generate_indicator_comparisons(indicators)

            logger.info(f"Generated {len(comparisons)} comparison analyses")

        except Exception as e:
            logger.error(f"Failed to generate comparisons: {str(e)}")

        return comparisons

    def _generate_county_comparisons(self, indicators: List[Indicator]) -> List[ComparisonResult]:
        """Generate county-based comparisons."""
        comparisons = []

        # Group indicators by name and year
        grouped = {}
        for ind in indicators:
            key = (ind.name, ind.year)
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(ind)

        # Generate comparisons for each indicator group
        for (indicator_name, year), group in grouped.items():
            if len(group) < 2:
                continue

            # Sort by value for ranking
            sorted_group = sorted(
                group, key=lambda x: x.value or 0, reverse=True)

            comparison_data = []
            values = []

            for ind in sorted_group:
                comparison_data.append({
                    'county': ind.county,
                    'value': ind.value,
                    'unit': ind.unit,
                    'rank': len(comparison_data) + 1
                })
                if ind.value is not None:
                    values.append(ind.value)

            # Calculate statistics
            if values:
                import statistics
                mean_val = statistics.mean(values)
                std_val = statistics.stdev(values) if len(values) > 1 else 0
                min_val = min(values)
                max_val = max(values)
            else:
                mean_val = std_val = min_val = max_val = None

            comparison = ComparisonResult(
                indicator_name=indicator_name,
                comparisons=comparison_data,
                mean_value=mean_val,
                std_deviation=std_val,
                min_value=min_val,
                max_value=max_val,
                summary=f"County comparison for {indicator_name} in {year}: {len(comparison_data)} counties analyzed"
            )

            comparisons.append(comparison)

        return comparisons

    def _generate_time_comparisons(self, indicators: List[Indicator]) -> List[ComparisonResult]:
        """Generate time-based comparisons."""
        comparisons = []

        # Group indicators by name and county
        grouped = {}
        for ind in indicators:
            key = (ind.name, ind.county)
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(ind)

        # Generate time comparisons for each group
        for (indicator_name, county), group in grouped.items():
            if len(group) < 2:
                continue

            # Sort by year
            sorted_group = sorted(group, key=lambda x: x.year)

            comparison_data = []
            for ind in sorted_group:
                comparison_data.append({
                    'year': ind.year,
                    'value': ind.value,
                    'unit': ind.unit
                })

            # Calculate trend
            values = [ind.value for ind in sorted_group if ind.value is not None]
            if len(values) >= 2:
                trend = "increasing" if values[-1] > values[0] else "decreasing"
                change = values[-1] - values[0] if len(values) >= 2 else 0
            else:
                trend = "insufficient_data"
                change = 0

            comparison = ComparisonResult(
                indicator_name=f"{indicator_name} - {county}",
                comparisons=comparison_data,
                summary=f"Time trend for {indicator_name} in {county}: {trend} (change: {change:.1f})"
            )

            comparisons.append(comparison)

        return comparisons

    def _generate_indicator_comparisons(self, indicators: List[Indicator]) -> List[ComparisonResult]:
        """Generate indicator-based comparisons."""
        comparisons = []

        # Group by county and year
        grouped = {}
        for ind in indicators:
            key = (ind.county, ind.year)
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(ind)

        # Generate indicator comparisons for each group
        for (county, year), group in grouped.items():
            if len(group) < 2:
                continue

            comparison_data = []
            for ind in group:
                comparison_data.append({
                    'indicator': ind.name,
                    'value': ind.value,
                    'unit': ind.unit,
                    'type': ind.type.value
                })

            comparison = ComparisonResult(
                indicator_name=f"Multiple indicators - {county} ({year})",
                comparisons=comparison_data,
                summary=f"Indicator comparison for {county} in {year}: {len(comparison_data)} indicators"
            )

            comparisons.append(comparison)

        return comparisons

    def _generate_response_content(self, original_query: str, query_analysis: Dict[str, Any],
                                   indicators: List[Indicator], comparisons: List[ComparisonResult]) -> Dict[str, Any]:
        """
        Generate comprehensive response content using AI.

        Args:
            original_query: Original user query
            query_analysis: Analyzed query intent
            indicators: Retrieved indicators
            comparisons: Generated comparisons

        Returns:
            Dictionary with response content
        """
        try:
            # Prepare indicators summary
            indicators_summary = f"{len(indicators)} indicators found"
            if indicators:
                counties = list(set(ind.county for ind in indicators))
                years = list(set(ind.year for ind in indicators))
                types = list(set(ind.type.value for ind in indicators))
                indicators_summary += f" across {len(counties)} counties, {len(years)} years, {len(types)} indicator types"

            # Prepare indicators data for prompt
            indicators_data = []
            for ind in indicators[:10]:  # Limit to first 10 for prompt
                indicators_data.append({
                    'name': ind.name,
                    'county': ind.county,
                    'year': ind.year,
                    'value': ind.value,
                    'unit': ind.unit,
                    'type': ind.type.value
                })

            prompt = self.response_generation_prompt.format(
                original_query=original_query,
                indicators_summary=indicators_summary,
                query_intent=query_analysis.get('query_intent', {}),
                indicators_data=json.dumps(indicators_data, indent=2)
            )

            response = self.client.generate_completion(
                prompt=prompt,
                temperature=0.2,
                max_tokens=2000
            )

            # Extract content from response
            if isinstance(response, dict) and 'choices' in response:
                content = response['choices'][0]['message']['content']
            else:
                content = response

            # Parse JSON response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                response_content = json.loads(json_match.group(0))
                return response_content
            else:
                # Fallback response
                return self._generate_fallback_response(indicators, comparisons)

        except Exception as e:
            logger.error(f"Failed to generate response content: {str(e)}")
            return self._generate_fallback_response(indicators, comparisons)

    def _generate_fallback_response(self, indicators: List[Indicator], comparisons: List[ComparisonResult]) -> Dict[str, Any]:
        """Generate fallback response when AI generation fails."""
        if not indicators:
            return {
                "executive_summary": "No relevant indicators found for the query.",
                "detailed_analysis": {"primary_findings": [], "comparisons": []},
                "key_insights": [],
                "recommendations": [],
                "data_context": {"survey_year": 2022, "counties_covered": [], "indicators_analyzed": []}
            }

        counties = list(set(ind.county for ind in indicators))
        years = list(set(ind.year for ind in indicators))
        indicator_names = list(set(ind.name for ind in indicators))

        return {
            "executive_summary": f"Found {len(indicators)} indicators across {len(counties)} counties and {len(years)} survey years.",
            "detailed_analysis": {
                "primary_findings": [f"{ind.name} in {ind.county}: {ind.value} {ind.unit or ''}" for ind in indicators[:5]],
                "comparisons": [comp.summary for comp in comparisons[:3]]
            },
            "key_insights": [
                f"Data available for counties: {', '.join(counties[:5])}",
                f"Survey years covered: {', '.join(map(str, sorted(years)))}"
            ],
            "recommendations": [
                "Review detailed indicator values for specific insights",
                "Consider additional analysis for comprehensive understanding"
            ],
            "data_context": {
                "survey_year": max(years) if years else 2022,
                "counties_covered": counties,
                "indicators_analyzed": indicator_names
            }
        }

    def _prepare_visualization_data(self, indicators: List[Indicator], request: QueryRequest) -> Tuple[Optional[Dict], Optional[Dict]]:
        """
        Prepare data for charts and maps.

        Args:
            indicators: Filtered indicators
            request: Query request with visualization preferences

        Returns:
            Tuple of (chart_data, map_data)
        """
        chart_data = None
        map_data = None

        if not indicators:
            return chart_data, map_data

        try:
            # Prepare chart data
            if request.include_charts:
                chart_data = {
                    "type": "bar",
                    "data": [],
                    "labels": [],
                    "title": "Health Indicators Comparison"
                }

                # Group by indicator name for charting
                grouped = {}
                for ind in indicators:
                    if ind.name not in grouped:
                        grouped[ind.name] = []
                    grouped[ind.name].append(ind)

                # Use first indicator group for chart
                if grouped:
                    first_indicator = list(grouped.keys())[0]
                    chart_indicators = grouped[first_indicator]

                    chart_data["title"] = first_indicator
                    chart_data["labels"] = [
                        ind.county for ind in chart_indicators]
                    chart_data["data"] = [
                        ind.value or 0 for ind in chart_indicators]
                    chart_data["unit"] = chart_indicators[0].unit if chart_indicators else ""

            # Prepare map data
            if request.include_maps:
                map_data = {
                    "type": "choropleth",
                    "counties": {},
                    "title": "Geographic Distribution",
                    "indicator": ""
                }

                # Use first indicator for mapping
                if indicators:
                    first_indicator_name = indicators[0].name
                    map_data["indicator"] = first_indicator_name
                    map_data["title"] = f"{first_indicator_name} by County"

                    for ind in indicators:
                        if ind.name == first_indicator_name and ind.value is not None:
                            map_data["counties"][ind.county] = {
                                "value": ind.value,
                                "unit": ind.unit,
                                "year": ind.year
                            }

        except Exception as e:
            logger.error(f"Failed to prepare visualization data: {str(e)}")

        return chart_data, map_data

    def _parse_confidence_score(self, confidence_value) -> float:
        """
        Parse confidence score from various formats to float.

        Args:
            confidence_value: Confidence value (string or float)

        Returns:
            Float confidence score between 0.0 and 1.0
        """
        if isinstance(confidence_value, (int, float)):
            return max(0.0, min(1.0, float(confidence_value)))

        if isinstance(confidence_value, str):
            confidence_map = {
                'high': 0.9,
                'medium': 0.7,
                'low': 0.4,
                'very_high': 0.95,
                'very_low': 0.2
            }
            return confidence_map.get(confidence_value.lower(), 0.7)

        return 0.7  # Default confidence

    def _fix_json_format(self, json_str: str) -> str:
        """
        Attempt to fix common JSON formatting issues.

        Args:
            json_str: Malformed JSON string

        Returns:
            Fixed JSON string
        """
        # Fix common issues
        fixed = json_str

        # Fix unquoted property names
        fixed = re.sub(r'(\w+):', r'"\1":', fixed)

        # Fix single quotes to double quotes
        fixed = fixed.replace("'", '"')

        # Fix trailing commas
        fixed = re.sub(r',\s*}', '}', fixed)
        fixed = re.sub(r',\s*]', ']', fixed)

        return fixed

    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """Extract JSON from response using multiple strategies."""
        # Strategy 1: Look for JSON code block
        json_match = re.search(
            r'```json\s*(\{.*?\})\s*```', response, re.DOTALL)
        if json_match:
            return json_match.group(1)

        # Strategy 2: Look for any JSON object
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json_match.group(0)

        # Strategy 3: Try to find JSON starting with specific keys
        for key in ['query_intent', 'indicators', 'executive_summary']:
            json_match = re.search(
                rf'\{{[^}}]*"{key}".*\}}', response, re.DOTALL)
            if json_match:
                return json_match.group(0)

        return None
