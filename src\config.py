"""Configuration management for the DHS RAQ system."""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Application configuration."""
    
    # API Configuration
    EURI_API_KEY: str = os.getenv("EURI_API_KEY", "")
    DEFAULT_LLM_MODEL: str = os.getenv("DEFAULT_LLM_MODEL", "gpt-4.1-nano")
    DEFAULT_EMBEDDING_MODEL: str = os.getenv("DEFAULT_EMBEDDING_MODEL", "text-embedding-3-small")
    
    # Vector Store Configuration
    VECTOR_STORE_TYPE: str = os.getenv("VECTOR_STORE_TYPE", "faiss")
    VECTOR_STORE_PATH: Path = Path(os.getenv("VECTOR_STORE_PATH", "./data/vector_store"))
    
    # Application Configuration
    APP_NAME: str = os.getenv("APP_NAME", "DHS RAQ System")
    APP_VERSION: str = os.getenv("APP_VERSION", "1.0.0")
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # Streamlit Configuration
    STREAMLIT_PORT: int = int(os.getenv("STREAMLIT_PORT", "8501"))
    STREAMLIT_HOST: str = os.getenv("STREAMLIT_HOST", "localhost")
    
    # Data Paths
    PDF_UPLOAD_PATH: Path = Path(os.getenv("PDF_UPLOAD_PATH", "./data/uploads"))
    EXTRACTED_DATA_PATH: Path = Path(os.getenv("EXTRACTED_DATA_PATH", "./data/extracted"))
    GEOJSON_PATH: Path = Path(os.getenv("GEOJSON_PATH", "./data/geo/kenya_counties.geojson"))
    
    # Processing Configuration
    MAX_PDF_SIZE_MB: int = int(os.getenv("MAX_PDF_SIZE_MB", "100"))
    CHUNK_SIZE: int = int(os.getenv("CHUNK_SIZE", "1000"))
    CHUNK_OVERLAP: int = int(os.getenv("CHUNK_OVERLAP", "200"))
    MAX_TOKENS_PER_REQUEST: int = int(os.getenv("MAX_TOKENS_PER_REQUEST", "4000"))
    
    # Visualization Configuration
    DEFAULT_MAP_CENTER_LAT: float = float(os.getenv("DEFAULT_MAP_CENTER_LAT", "0.0236"))
    DEFAULT_MAP_CENTER_LON: float = float(os.getenv("DEFAULT_MAP_CENTER_LON", "37.9062"))
    DEFAULT_MAP_ZOOM: int = int(os.getenv("DEFAULT_MAP_ZOOM", "6"))
    
    @classmethod
    def validate(cls) -> bool:
        """Validate required configuration."""
        if not cls.EURI_API_KEY:
            raise ValueError("EURI_API_KEY is required")
        
        # Create directories if they don't exist
        cls.PDF_UPLOAD_PATH.mkdir(parents=True, exist_ok=True)
        cls.EXTRACTED_DATA_PATH.mkdir(parents=True, exist_ok=True)
        cls.VECTOR_STORE_PATH.mkdir(parents=True, exist_ok=True)
        cls.GEOJSON_PATH.parent.mkdir(parents=True, exist_ok=True)
        
        return True

# Global config instance
config = Config()
