"""Test the advanced QueryAgentTool with natural language processing."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_query_agent():
    """Test the query agent with sample data and queries."""
    print("🤖 Testing Advanced Query Agent Tool")
    print("=" * 60)
    
    try:
        from src.config import config
        from src.tools.query_agent import QueryAgentTool
        from src.tools.text_agent import TextAgentTool
        from src.tools.pdf_loader import PDFLoaderTool
        from src.tools.embedding_generator import EmbeddingGeneratorTool
        from src.tools.vector_store import VectorStoreTool
        from src.models.query import QueryRequest
        from src.models.extraction import PDFPage, PageType
        
        # Check API key
        if not config.EURI_API_KEY or config.EURI_API_KEY == "your_euriai_api_key_here":
            print("⚠ EURI_API_KEY not set - cannot test query agent")
            return False
        
        print("🔧 Setting up test environment...")
        
        # Initialize components
        query_agent = QueryAgentTool()
        text_agent = TextAgentTool()
        embedding_generator = EmbeddingGeneratorTool()
        vector_store = VectorStoreTool()
        
        print("✓ Query agent initialized")
        
        # Step 1: Create sample data for testing
        print("\n📊 Creating sample indicator data...")
        
        sample_content = """
        CHILD HEALTH AND NUTRITION INDICATORS - 2022 KDHS
        
        Stunting prevalence among children under 5 years varies significantly across Western Kenya counties:
        - Kakamega County: 22.3% (95% CI: 19.8-24.9)
        - Vihiga County: 18.1% (95% CI: 15.2-21.3)
        - Bungoma County: 24.7% (95% CI: 21.9-27.8)
        - Busia County: 19.8% (95% CI: 17.1-22.8)
        
        Immunization coverage (fully immunized children 12-23 months):
        - Kakamega County: 88.0%
        - Vihiga County: 83.4%
        - Bungoma County: 85.2%
        - Busia County: 81.7%
        
        Maternal health indicators show improvement:
        - Antenatal care (4+ visits) in Kakamega: 67.8%
        - Skilled birth attendance in Vihiga: 91.2%
        """
        
        # Extract indicators from sample content
        test_page = PDFPage(
            page_number=1,
            page_type=PageType.TEXT_HEAVY,
            text_content=sample_content,
            extraction_method="test",
            confidence_score=1.0
        )
        
        indicators = text_agent.extract_indicators(test_page, "test_document.pdf")
        print(f"✓ Extracted {len(indicators)} sample indicators")
        
        if not indicators:
            print("❌ No indicators extracted - cannot test query agent")
            return False
        
        # Step 2: Store indicators in vector store
        print("\n🧠 Storing indicators in vector store...")
        
        vector_store.clear()
        embedding_records = embedding_generator.create_embedding_records_batch(indicators)
        vector_store.add_records(embedding_records)
        
        stats = vector_store.get_stats()
        print(f"✓ Stored {stats['total_records']} indicators in vector store")
        
        # Step 3: Test different types of queries
        test_queries = [
            {
                "query": "What is the stunting prevalence in Kakamega compared to Vihiga?",
                "description": "County comparison query"
            },
            {
                "query": "Show me immunization coverage across Western Kenya counties",
                "description": "Regional overview query"
            },
            {
                "query": "Compare child health indicators between Bungoma and Busia",
                "description": "Multi-indicator comparison"
            },
            {
                "query": "What are the maternal health statistics for Kakamega?",
                "description": "Specific indicator type query"
            }
        ]
        
        print(f"\n🔍 Testing {len(test_queries)} different query types...")
        
        for i, test_case in enumerate(test_queries, 1):
            print(f"\n--- Test Query {i}: {test_case['description']} ---")
            print(f"Query: '{test_case['query']}'")
            
            # Create query request
            request = QueryRequest(
                query=test_case['query'],
                include_charts=True,
                include_maps=True,
                include_summary=True,
                max_results=20
            )
            
            # Process query
            response = query_agent.process_query(request)
            
            print(f"✓ Processing time: {response.processing_time_ms:.1f}ms")
            print(f"✓ Confidence score: {response.confidence_score:.2f}")
            print(f"✓ Indicators found: {response.total_indicators}")
            
            if response.summary:
                print(f"📝 Summary: {response.summary}")
            
            if response.insights:
                print(f"💡 Key insights:")
                for insight in response.insights[:2]:  # Show first 2
                    print(f"   - {insight}")
            
            if response.comparisons:
                print(f"📊 Comparisons: {len(response.comparisons)} generated")
                for comp in response.comparisons[:1]:  # Show first comparison
                    print(f"   - {comp.summary}")
            
            if response.chart_data:
                print(f"📈 Chart data prepared: {response.chart_data.get('title', 'Chart')}")
            
            if response.map_data:
                print(f"🗺️ Map data prepared: {len(response.map_data.get('counties', {}))} counties")
            
            print("✓ Query processed successfully")
        
        # Step 4: Test query analysis capabilities
        print(f"\n🧠 Testing query analysis capabilities...")
        
        analysis_query = "Compare stunting and immunization in Western Kenya counties over time"
        analysis = query_agent._analyze_query_intent(analysis_query)
        
        if analysis:
            query_intent = analysis.get('query_intent', {})
            print(f"✓ Query intent analysis:")
            print(f"   - Primary intent: {query_intent.get('primary_intent')}")
            print(f"   - Indicators requested: {query_intent.get('indicators_requested')}")
            print(f"   - Counties requested: {query_intent.get('counties_requested')}")
            print(f"   - Comparison type: {query_intent.get('comparison_type')}")
            
            confidence = analysis.get('confidence_assessment', {})
            print(f"   - Overall confidence: {confidence.get('overall_confidence')}")
        
        # Step 5: Test error handling
        print(f"\n🛡️ Testing error handling...")
        
        error_request = QueryRequest(
            query="Show me data for NonExistentCounty",
            max_results=10
        )
        
        error_response = query_agent.process_query(error_request)
        print(f"✓ Error handling: {error_response.total_indicators} indicators found")
        print(f"✓ Error response generated in {error_response.processing_time_ms:.1f}ms")
        
        # Clean up
        vector_store.clear()
        
        print("\n" + "=" * 60)
        print("🎉 QUERY AGENT TEST PASSED!")
        print("✓ Natural language query understanding")
        print("✓ Vector similarity search integration")
        print("✓ Comprehensive response generation")
        print("✓ Comparison analysis")
        print("✓ Visualization data preparation")
        print("✓ Error handling")
        print("\n🚀 Ready for Streamlit UI integration!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ QUERY AGENT TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_query_agent()
    sys.exit(0 if success else 1)
