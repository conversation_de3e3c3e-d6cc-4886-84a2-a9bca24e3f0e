"""Choropleth map tool for creating geographic visualizations of Kenya health indicators."""

import json
from typing import List, Dict, Any, Optional
import logging

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

from ..models.indicator import Indicator
from ..models.county import KENYA_COUNTIES, KENYA_REGIONS
from ..config import config

logger = logging.getLogger(__name__)


class ChoroplethMapTool:
    """Tool for creating choropleth maps of Kenya with health indicator data."""
    
    def __init__(self):
        """Initialize the choropleth map tool."""
        self.county_data = KENYA_COUNTIES
        self.region_data = KENYA_REGIONS
        
        # Kenya geographic bounds
        self.kenya_bounds = {
            "lat_min": -4.7,
            "lat_max": 5.5,
            "lon_min": 33.9,
            "lon_max": 41.9
        }
        
        # Color scales for different indicator types
        self.color_scales = {
            'nutrition': 'Reds',
            'maternal_health': 'Purples',
            'child_health': 'Blues',
            'immunization': 'Greens',
            'family_planning': 'Oranges',
            'hiv_aids': 'YlOrRd',
            'malaria': 'YlGnBu',
            'water_sanitation': 'BuGn',
            'demographic': 'Viridis',
            'socioeconomic': 'Plasma',
            'default': 'Viridis'
        }

    def create_choropleth_map(self, indicators: List[Indicator], 
                             title: Optional[str] = None,
                             indicator_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a choropleth map from health indicators.
        
        Args:
            indicators: List of indicators with county-level data
            title: Map title
            indicator_name: Specific indicator to map (if multiple indicators)
            
        Returns:
            Dictionary with map configuration and Plotly JSON
        """
        if not indicators:
            return self._create_empty_map("No data available for mapping")
        
        try:
            # Filter and prepare data
            county_indicators = [ind for ind in indicators if ind.county and ind.county != 'National']
            
            if not county_indicators:
                return self._create_empty_map("No county-level data available")
            
            # Convert to DataFrame
            df = self._indicators_to_dataframe(county_indicators)
            
            # Filter by specific indicator if requested
            if indicator_name:
                df = df[df['indicator_name'] == indicator_name]
                if df.empty:
                    return self._create_empty_map(f"No data found for indicator: {indicator_name}")
            
            # Aggregate data by county (take mean if multiple values)
            county_data = df.groupby('county').agg({
                'value': 'mean',
                'unit': 'first',
                'indicator_name': 'first',
                'type': 'first'
            }).reset_index()
            
            # Add geographic coordinates
            county_data = self._add_geographic_data(county_data)
            
            # Create the map
            map_title = title or f"{county_data['indicator_name'].iloc[0]} by County"
            indicator_type = county_data['type'].iloc[0] if not county_data.empty else 'default'
            color_scale = self.color_scales.get(indicator_type, self.color_scales['default'])
            
            # Create choropleth map
            fig = self._create_plotly_choropleth(county_data, map_title, color_scale)
            
            return {
                "success": True,
                "title": map_title,
                "indicator_type": indicator_type,
                "counties_mapped": len(county_data),
                "data_range": {
                    "min": county_data['value'].min(),
                    "max": county_data['value'].max(),
                    "mean": county_data['value'].mean()
                },
                "unit": county_data['unit'].iloc[0] if not county_data.empty else '',
                "plotly_json": fig.to_json(),
                "data": county_data.to_dict('records')
            }
            
        except Exception as e:
            logger.error(f"Failed to create choropleth map: {str(e)}")
            return self._create_empty_map(f"Error creating map: {str(e)}")

    def _indicators_to_dataframe(self, indicators: List[Indicator]) -> pd.DataFrame:
        """Convert indicators to pandas DataFrame."""
        data = []
        
        for indicator in indicators:
            data.append({
                'indicator_name': indicator.name,
                'county': indicator.county,
                'value': indicator.value,
                'unit': indicator.unit or '',
                'type': indicator.type.value,
                'year': indicator.year
            })
        
        return pd.DataFrame(data)

    def _add_geographic_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add geographic coordinates and region information to county data."""
        # Add latitude, longitude, and region for each county
        df['latitude'] = df['county'].map(lambda x: self.county_data.get(x, {}).get('latitude'))
        df['longitude'] = df['county'].map(lambda x: self.county_data.get(x, {}).get('longitude'))
        df['region'] = df['county'].map(lambda x: self.county_data.get(x, {}).get('region'))
        df['county_code'] = df['county'].map(lambda x: self.county_data.get(x, {}).get('code'))
        
        # Remove counties without geographic data
        df = df.dropna(subset=['latitude', 'longitude'])
        
        return df

    def _create_plotly_choropleth(self, df: pd.DataFrame, title: str, color_scale: str) -> go.Figure:
        """Create Plotly choropleth map."""
        # For now, create a scatter map since we don't have county boundary GeoJSON
        # In production, this would use actual county boundaries
        
        fig = px.scatter_mapbox(
            df,
            lat='latitude',
            lon='longitude',
            color='value',
            size='value',
            hover_name='county',
            hover_data={
                'value': ':.2f',
                'unit': True,
                'region': True,
                'latitude': False,
                'longitude': False
            },
            color_continuous_scale=color_scale,
            title=title,
            mapbox_style='open-street-map',
            zoom=5.5,
            center={'lat': 0.0236, 'lon': 37.9062}  # Center of Kenya
        )
        
        # Update layout
        fig.update_layout(
            height=600,
            margin={"r": 0, "t": 50, "l": 0, "b": 0},
            coloraxis_colorbar=dict(
                title=f"Value ({df['unit'].iloc[0] if not df.empty else ''})"
            )
        )
        
        return fig

    def create_regional_map(self, indicators: List[Indicator], 
                           title: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a regional-level choropleth map.
        
        Args:
            indicators: List of indicators with county-level data
            title: Map title
            
        Returns:
            Dictionary with regional map configuration
        """
        try:
            # Convert to DataFrame and add geographic data
            county_indicators = [ind for ind in indicators if ind.county and ind.county != 'National']
            if not county_indicators:
                return self._create_empty_map("No county-level data for regional mapping")
            
            df = self._indicators_to_dataframe(county_indicators)
            df = self._add_geographic_data(df)
            
            # Aggregate by region
            regional_data = df.groupby('region').agg({
                'value': 'mean',
                'unit': 'first',
                'indicator_name': 'first',
                'type': 'first'
            }).reset_index()
            
            # Add regional coordinates (center of region)
            regional_coords = self._get_regional_coordinates()
            regional_data = regional_data.merge(regional_coords, on='region', how='left')
            
            # Create regional map
            map_title = title or f"{regional_data['indicator_name'].iloc[0]} by Region"
            indicator_type = regional_data['type'].iloc[0] if not regional_data.empty else 'default'
            color_scale = self.color_scales.get(indicator_type, self.color_scales['default'])
            
            fig = px.scatter_mapbox(
                regional_data,
                lat='latitude',
                lon='longitude',
                color='value',
                size='value',
                hover_name='region',
                hover_data={
                    'value': ':.2f',
                    'unit': True,
                    'latitude': False,
                    'longitude': False
                },
                color_continuous_scale=color_scale,
                title=map_title,
                mapbox_style='open-street-map',
                zoom=5,
                center={'lat': 0.0236, 'lon': 37.9062}
            )
            
            fig.update_layout(
                height=600,
                margin={"r": 0, "t": 50, "l": 0, "b": 0}
            )
            
            return {
                "success": True,
                "title": map_title,
                "indicator_type": indicator_type,
                "regions_mapped": len(regional_data),
                "data_range": {
                    "min": regional_data['value'].min(),
                    "max": regional_data['value'].max(),
                    "mean": regional_data['value'].mean()
                },
                "unit": regional_data['unit'].iloc[0] if not regional_data.empty else '',
                "plotly_json": fig.to_json(),
                "data": regional_data.to_dict('records')
            }
            
        except Exception as e:
            logger.error(f"Failed to create regional map: {str(e)}")
            return self._create_empty_map(f"Error creating regional map: {str(e)}")

    def _get_regional_coordinates(self) -> pd.DataFrame:
        """Get approximate coordinates for each region."""
        # Calculate regional centers from county coordinates
        regional_coords = []
        
        for region_name, region_info in self.region_data.items():
            counties_in_region = region_info.get('counties', [])
            
            if counties_in_region:
                # Calculate average coordinates of counties in region
                lats = []
                lons = []
                
                for county in counties_in_region:
                    county_info = self.county_data.get(county, {})
                    if county_info.get('latitude') and county_info.get('longitude'):
                        lats.append(county_info['latitude'])
                        lons.append(county_info['longitude'])
                
                if lats and lons:
                    regional_coords.append({
                        'region': region_name,
                        'latitude': sum(lats) / len(lats),
                        'longitude': sum(lons) / len(lons)
                    })
        
        return pd.DataFrame(regional_coords)

    def create_comparison_map(self, indicators_list: List[List[Indicator]], 
                             titles: List[str] = None) -> Dict[str, Any]:
        """
        Create side-by-side comparison maps.
        
        Args:
            indicators_list: List of indicator lists to compare
            titles: Titles for each map
            
        Returns:
            Dictionary with comparison map configuration
        """
        try:
            if len(indicators_list) < 2:
                return self._create_empty_map("Need at least 2 indicator sets for comparison")
            
            # Create individual maps
            maps = []
            for i, indicators in enumerate(indicators_list):
                title = titles[i] if titles and i < len(titles) else f"Map {i+1}"
                map_data = self.create_choropleth_map(indicators, title)
                if map_data["success"]:
                    maps.append(map_data)
            
            if not maps:
                return self._create_empty_map("No valid maps created for comparison")
            
            # For now, return the first map with comparison metadata
            # In production, this would create a subplot with multiple maps
            comparison_result = maps[0].copy()
            comparison_result["comparison_data"] = {
                "total_maps": len(maps),
                "map_titles": [m["title"] for m in maps],
                "data_ranges": [m["data_range"] for m in maps]
            }
            
            return comparison_result
            
        except Exception as e:
            logger.error(f"Failed to create comparison map: {str(e)}")
            return self._create_empty_map(f"Error creating comparison map: {str(e)}")

    def _create_empty_map(self, message: str) -> Dict[str, Any]:
        """Create an empty map with error message."""
        fig = go.Figure()
        
        # Add Kenya outline
        fig.add_trace(go.Scattermapbox(
            lat=[self.kenya_bounds["lat_min"], self.kenya_bounds["lat_max"]],
            lon=[self.kenya_bounds["lon_min"], self.kenya_bounds["lon_max"]],
            mode='markers',
            marker=dict(size=1, opacity=0),
            showlegend=False
        ))
        
        fig.update_layout(
            title=message,
            mapbox=dict(
                style='open-street-map',
                center={'lat': 0.0236, 'lon': 37.9062},
                zoom=5
            ),
            height=600,
            margin={"r": 0, "t": 50, "l": 0, "b": 0}
        )
        
        return {
            "success": False,
            "title": message,
            "indicator_type": "none",
            "counties_mapped": 0,
            "data_range": {"min": 0, "max": 0, "mean": 0},
            "unit": "",
            "plotly_json": fig.to_json(),
            "data": []
        }

    def get_available_counties(self) -> List[str]:
        """Get list of available counties for mapping."""
        return list(self.county_data.keys())

    def get_available_regions(self) -> List[str]:
        """Get list of available regions for mapping."""
        return list(self.region_data.keys())

    def validate_geographic_data(self, indicators: List[Indicator]) -> Dict[str, Any]:
        """
        Validate that indicators have mappable geographic data.
        
        Args:
            indicators: List of indicators to validate
            
        Returns:
            Validation report
        """
        total_indicators = len(indicators)
        county_indicators = [ind for ind in indicators if ind.county and ind.county != 'National']
        mappable_counties = [ind.county for ind in county_indicators if ind.county in self.county_data]
        
        return {
            "total_indicators": total_indicators,
            "county_level_indicators": len(county_indicators),
            "mappable_indicators": len(mappable_counties),
            "mappable_counties": list(set(mappable_counties)),
            "unmappable_counties": list(set([ind.county for ind in county_indicators]) - set(mappable_counties)),
            "coverage_percentage": len(mappable_counties) / len(county_indicators) * 100 if county_indicators else 0
        }
