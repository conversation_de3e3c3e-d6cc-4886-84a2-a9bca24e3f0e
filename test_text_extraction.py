"""Test the enhanced TextAgentTool with comprehensive prompting."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_text_agent_extraction():
    """Test text agent with sample DHS content."""
    print("🔍 Testing Enhanced Text Agent Tool")
    print("=" * 50)
    
    try:
        from src.config import config
        from src.tools.text_agent import TextAgentTool
        from src.tools.pdf_loader import PDFLoaderTool
        from src.models.extraction import PDFPage, PageType
        
        # Check API key
        if not config.EURI_API_KEY or config.EURI_API_KEY == "your_euriai_api_key_here":
            print("⚠ EURI_API_KEY not set - cannot test text extraction")
            return False
        
        # Initialize text agent
        text_agent = TextAgentTool()
        print("✓ Text agent initialized")
        
        # Test with sample DHS-like content
        sample_content = """
        CHAPTER 10: CHILD HEALTH AND NUTRITION
        
        This chapter presents findings on child health and nutrition indicators from the 2022 Kenya Demographic and Health Survey (KDHS). The survey collected data on stunting, wasting, and underweight among children under 5 years of age.
        
        STUNTING PREVALENCE BY COUNTY
        
        Stunting prevalence varies significantly across counties. In Kakamega County, 22.3% of children under 5 years were stunted in 2022, compared to 18.1% in Vihiga County. The national average was 18.0%.
        
        Bungoma County recorded a stunting prevalence of 24.7% among children aged 6-59 months, while Busia County had 19.8%. These figures represent a decline from the 2014 KDHS when Kakamega had 26.1% and Vihiga had 21.4%.
        
        IMMUNIZATION COVERAGE
        
        Full immunization coverage among children aged 12-23 months was 88.0% in Kakamega County and 83.4% in Vihiga County in 2022. Bungoma achieved 85.2% coverage while Busia reached 81.7%.
        
        MATERNAL HEALTH INDICATORS
        
        Antenatal care coverage (at least 4 visits) was 67.8% in Kakamega County and 72.1% in Vihiga County. Skilled birth attendance was 89.3% in Kakamega and 91.2% in Vihiga.
        """
        
        # Create a mock PDFPage
        test_page = PDFPage(
            page_number=15,
            page_type=PageType.TEXT_HEAVY,
            text_content=sample_content,
            extraction_method="test",
            confidence_score=1.0
        )
        
        print("✓ Sample DHS content prepared")
        print(f"Content length: {len(sample_content)} characters")
        
        # Test indicator extraction
        print("\nExtracting indicators...")
        indicators = text_agent.extract_indicators(test_page, "FR380.pdf")
        
        print(f"✓ Extracted {len(indicators)} indicators")
        
        if indicators:
            print("\n📊 EXTRACTED INDICATORS:")
            for i, indicator in enumerate(indicators, 1):
                print(f"\n{i}. {indicator.name}")
                print(f"   County: {indicator.county}")
                print(f"   Year: {indicator.year}")
                print(f"   Value: {indicator.value} {indicator.unit or ''}")
                print(f"   Type: {indicator.type.value}")
                if indicator.age_group:
                    print(f"   Age Group: {indicator.age_group}")
                if indicator.summary:
                    print(f"   Summary: {indicator.summary}")
        
        # Test with real PDF content
        print(f"\n🔍 Testing with real PDF content...")
        
        # Load a small PDF
        pdf_files = list(config.PDF_UPLOAD_PATH.glob("*.pdf"))
        if pdf_files:
            # Use smallest PDF
            pdf_files.sort(key=lambda x: x.stat().st_size)
            pdf_path = pdf_files[0]
            
            print(f"Loading: {pdf_path.name}")
            
            loader = PDFLoaderTool()
            result = loader.load_pdf(str(pdf_path))
            
            # Find text-heavy pages
            text_pages = [p for p in result.pages if p.page_type == PageType.TEXT_HEAVY][:3]  # Test first 3
            
            if text_pages:
                print(f"Testing extraction on {len(text_pages)} text pages...")
                
                all_indicators = []
                for page in text_pages:
                    page_indicators = text_agent.extract_indicators(page, pdf_path.name)
                    all_indicators.extend(page_indicators)
                    print(f"  Page {page.page_number}: {len(page_indicators)} indicators")
                
                print(f"\n✓ Total indicators from PDF: {len(all_indicators)}")
                
                if all_indicators:
                    print("\n📋 SAMPLE REAL INDICATORS:")
                    for indicator in all_indicators[:3]:  # Show first 3
                        print(f"- {indicator.name} in {indicator.county}: {indicator.value} {indicator.unit or ''}")
            else:
                print("⚠ No text-heavy pages found in PDF")
        
        # Test embedding integration
        if indicators:
            print(f"\n🧠 Testing embedding integration...")
            from src.tools.embedding_generator import EmbeddingGeneratorTool
            from src.tools.vector_store import VectorStoreTool
            
            generator = EmbeddingGeneratorTool()
            store = VectorStoreTool()
            
            # Create embedding records
            records = generator.create_embedding_records_batch(indicators[:2])  # Test with first 2
            
            # Store in vector store
            store.clear()
            store.add_records(records)
            
            stats = store.get_stats()
            print(f"✓ Stored {stats['total_records']} indicator embeddings")
            
            # Test search
            query_embedding = generator.generate_embedding("stunting in Western Kenya")
            results = store.search_by_embedding(query_embedding, top_k=1)
            
            if results:
                print(f"✓ Search successful - similarity: {results[0].similarity_score:.4f}")
                print(f"✓ Found: {results[0].record.indicator_name}")
            
            store.clear()
        
        print("\n🎉 TEXT EXTRACTION TEST PASSED!")
        print("✓ Enhanced prompting working")
        print("✓ Indicator extraction from sample content")
        print("✓ Real PDF content processing")
        print("✓ Embedding integration")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEXT EXTRACTION TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_text_agent_extraction()
    sys.exit(0 if success else 1)
