"""Indicator data models."""

from enum import Enum
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, validator


class IndicatorType(str, Enum):
    """Types of health indicators."""
    CHILD_HEALTH = "child_health"
    MATERNAL_HEALTH = "maternal_health"
    NUTRITION = "nutrition"
    IMMUNIZATION = "immunization"
    FAMILY_PLANNING = "family_planning"
    HIV_AIDS = "hiv_aids"
    MALARIA = "malaria"
    WATER_SANITATION = "water_sanitation"
    DEMOGRAPHIC = "demographic"
    SOCIOECONOMIC = "socioeconomic"
    OTHER = "other"


class IndicatorSource(BaseModel):
    """Source information for an indicator."""
    document: str = Field(..., description="Source document name")
    page_number: Optional[int] = Field(None, description="Page number in document")
    table_id: Optional[str] = Field(None, description="Table identifier")
    figure_id: Optional[str] = Field(None, description="Figure identifier")
    section: Optional[str] = Field(None, description="Document section")


class Indicator(BaseModel):
    """Core indicator model with all metadata."""
    
    # Core identification
    id: Optional[str] = Field(None, description="Unique identifier")
    name: str = Field(..., description="Indicator name")
    type: IndicatorType = Field(..., description="Indicator category")
    
    # Geographic and temporal
    county: str = Field(..., description="County name")
    year: int = Field(..., description="Survey year")
    
    # Value and metadata
    value: Optional[float] = Field(None, description="Numeric value")
    value_text: Optional[str] = Field(None, description="Text representation of value")
    unit: Optional[str] = Field(None, description="Unit of measurement")
    
    # Additional context
    age_group: Optional[str] = Field(None, description="Age group specification")
    gender: Optional[str] = Field(None, description="Gender specification")
    urban_rural: Optional[str] = Field(None, description="Urban/rural classification")
    
    # Source and quality
    source: IndicatorSource = Field(..., description="Source information")
    confidence_interval: Optional[Dict[str, float]] = Field(None, description="CI bounds")
    sample_size: Optional[int] = Field(None, description="Sample size")
    note: Optional[str] = Field(None, description="Additional notes")
    
    # Processing metadata
    extraction_method: Optional[str] = Field(None, description="How this was extracted")
    extraction_confidence: Optional[float] = Field(None, description="Extraction confidence score")
    created_at: datetime = Field(default_factory=datetime.now)
    
    # Embedding and search
    summary: Optional[str] = Field(None, description="Summary for embedding")
    keywords: List[str] = Field(default_factory=list, description="Search keywords")
    
    @validator('value')
    def validate_value(cls, v, values):
        """Ensure value is reasonable."""
        if v is not None:
            if v < 0:
                raise ValueError("Value cannot be negative")
            # Most health indicators are percentages or rates
            if v > 1000:
                raise ValueError("Value seems unreasonably high")
        return v
    
    @validator('year')
    def validate_year(cls, v):
        """Ensure year is reasonable."""
        if v < 1990 or v > 2030:
            raise ValueError("Year must be between 1990 and 2030")
        return v
    
    def to_embedding_text(self) -> str:
        """Generate text representation for embedding."""
        parts = [
            f"Indicator: {self.name}",
            f"County: {self.county}",
            f"Year: {self.year}",
            f"Type: {self.type.value}",
        ]
        
        if self.value is not None:
            parts.append(f"Value: {self.value}")
        if self.unit:
            parts.append(f"Unit: {self.unit}")
        if self.age_group:
            parts.append(f"Age group: {self.age_group}")
        if self.gender:
            parts.append(f"Gender: {self.gender}")
        if self.summary:
            parts.append(f"Summary: {self.summary}")
        if self.note:
            parts.append(f"Note: {self.note}")
            
        return " | ".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Indicator":
        """Create from dictionary."""
        return cls(**data)
