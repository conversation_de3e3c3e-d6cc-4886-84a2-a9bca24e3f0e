"""Query and response data models."""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from .indicator import Indicator
from .embedding import QueryResult


class QueryRequest(BaseModel):
    """Request for natural language query."""

    query: str = Field(..., description="Natural language query")

    # Optional filters
    counties: Optional[List[str]] = Field(
        None, description="Filter by specific counties")
    years: Optional[List[int]] = Field(
        None, description="Filter by specific years")
    indicator_types: Optional[List[str]] = Field(
        None, description="Filter by indicator types")

    # Response preferences
    include_charts: bool = Field(
        default=True, description="Include charts in response")
    include_maps: bool = Field(
        default=True, description="Include maps in response")
    include_summary: bool = Field(
        default=True, description="Include AI summary")
    max_results: int = Field(
        default=50, description="Maximum indicators to return")

    # Processing options
    comparison_mode: bool = Field(
        default=False, description="Enable comparison analysis")
    trend_analysis: bool = Field(
        default=False, description="Enable trend analysis")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()


class ComparisonResult(BaseModel):
    """Result of comparing indicators across counties/years."""

    indicator_name: str = Field(..., description="Name of compared indicator")
    comparisons: List[Dict[str, Any]
                      ] = Field(..., description="Comparison data")
    summary: Optional[str] = Field(
        None, description="AI-generated comparison summary")

    # Statistical analysis
    mean_value: Optional[float] = Field(
        None, description="Mean value across comparisons")
    std_deviation: Optional[float] = Field(
        None, description="Standard deviation")
    min_value: Optional[float] = Field(None, description="Minimum value")
    max_value: Optional[float] = Field(None, description="Maximum value")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()


class QueryResponse(BaseModel):
    """Response to natural language query."""

    # Request information
    original_query: str = Field(..., description="Original query text")
    processed_query: Optional[str] = Field(
        None, description="Processed/normalized query")

    # Results
    indicators: List[Indicator] = Field(...,
                                        description="Retrieved indicators")
    total_indicators: int = Field(...,
                                  description="Total number of indicators found")

    # Analysis results
    comparisons: List[ComparisonResult] = Field(
        default_factory=list, description="Comparison results")
    trends: List[Dict[str, Any]] = Field(
        default_factory=list, description="Trend analysis results")

    # AI-generated content
    summary: Optional[str] = Field(None, description="AI-generated summary")
    insights: List[str] = Field(
        default_factory=list, description="Key insights")

    # Visualization data
    chart_data: Optional[Dict[str, Any]] = Field(
        None, description="Data for charts")
    map_data: Optional[Dict[str, Any]] = Field(
        None, description="Data for maps")

    # Metadata
    search_results: List[QueryResult] = Field(
        default_factory=list, description="Vector search results")
    processing_time_ms: float = Field(..., description="Total processing time")
    created_at: datetime = Field(default_factory=datetime.now)

    # Quality metrics
    confidence_score: Optional[float] = Field(
        None, description="Overall confidence in results")
    coverage_score: Optional[float] = Field(
        None, description="Query coverage score")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "original_query": self.original_query,
            "processed_query": self.processed_query,
            "indicators": [ind.to_dict() for ind in self.indicators],
            "total_indicators": self.total_indicators,
            "comparisons": [comp.to_dict() for comp in self.comparisons],
            "trends": self.trends,
            "summary": self.summary,
            "insights": self.insights,
            "chart_data": self.chart_data,
            "map_data": self.map_data,
            "search_results": [res.to_dict() for res in self.search_results],
            "processing_time_ms": self.processing_time_ms,
            "created_at": self.created_at.isoformat(),
            "confidence_score": self.confidence_score,
            "coverage_score": self.coverage_score
        }

    def get_counties(self) -> List[str]:
        """Get unique counties from indicators."""
        return list(set(ind.county for ind in self.indicators))

    def get_years(self) -> List[int]:
        """Get unique years from indicators."""
        return sorted(list(set(ind.year for ind in self.indicators)))

    def get_indicator_types(self) -> List[str]:
        """Get unique indicator types from indicators."""
        return list(set(ind.type.value for ind in self.indicators))

    def filter_by_county(self, county: str) -> List[Indicator]:
        """Filter indicators by county."""
        return [ind for ind in self.indicators if ind.county.lower() == county.lower()]

    def filter_by_year(self, year: int) -> List[Indicator]:
        """Filter indicators by year."""
        return [ind for ind in self.indicators if ind.year == year]

    def filter_by_type(self, indicator_type: str) -> List[Indicator]:
        """Filter indicators by type."""
        return [ind for ind in self.indicators if ind.type.value == indicator_type]


class ChartData(BaseModel):
    """Chart data for visualizations."""

    type: str = Field(..., description="Chart type (bar, line, pie, etc.)")
    data: List[Dict[str, Any]] = Field(..., description="Chart data points")
    labels: List[str] = Field(..., description="Chart labels")
    values: List[float] = Field(..., description="Chart values")
    title: str = Field(..., description="Chart title")
    unit: str = Field(default="", description="Unit of measurement")
    plotly_json: Optional[str] = Field(
        None, description="Plotly JSON configuration")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()
