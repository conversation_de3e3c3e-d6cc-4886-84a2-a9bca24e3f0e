"""PDF loading and page extraction tool."""

import os
import fitz  # PyMuPDF
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from ..models.extraction import PDFPage, PageType, ExtractionResult
from ..config import config

logger = logging.getLogger(__name__)


class PDFLoaderTool:
    """Tool for loading PDFs and extracting pages."""

    def __init__(self):
        """Initialize the PDF loader."""
        self.supported_formats = ['.pdf']

    def load_pdf(self, pdf_path: str) -> ExtractionResult:
        """
        Load a PDF and extract basic page information.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            ExtractionResult with basic page information
        """
        pdf_path = Path(pdf_path)

        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        if pdf_path.suffix.lower() not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {pdf_path.suffix}")

        # Check file size
        file_size_mb = pdf_path.stat().st_size / (1024 * 1024)
        if file_size_mb > config.MAX_PDF_SIZE_MB:
            raise ValueError(
                f"PDF file too large: {file_size_mb:.1f}MB > {config.MAX_PDF_SIZE_MB}MB")

        logger.info(f"Loading PDF: {pdf_path}")
        start_time = datetime.now()

        try:
            # Open PDF with PyMuPDF
            doc = fitz.open(str(pdf_path))
            total_pages = len(doc)

            logger.info(f"PDF has {total_pages} pages")

            pages = []
            errors = []
            warnings = []

            for page_num in range(total_pages):
                try:
                    page = self._extract_page(doc, page_num)
                    pages.append(page)
                except Exception as e:
                    error_msg = f"Error extracting page {page_num + 1}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            doc.close()

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000

            # Calculate success rate
            success_rate = len(pages) / total_pages if total_pages > 0 else 0.0

            result = ExtractionResult(
                pdf_path=str(pdf_path),
                pdf_name=pdf_path.name,
                total_pages=total_pages,
                pages=pages,
                extraction_started=start_time,
                extraction_completed=end_time,
                total_processing_time_ms=processing_time,
                success_rate=success_rate,
                error_count=len(errors),
                warning_count=len(warnings),
                errors=errors,
                warnings=warnings
            )

            logger.info(
                f"PDF loading completed in {processing_time:.1f}ms with {success_rate:.1%} success rate")
            return result

        except Exception as e:
            logger.error(f"Failed to load PDF {pdf_path}: {str(e)}")
            raise

    def _extract_page(self, doc: fitz.Document, page_num: int) -> PDFPage:
        """
        Extract information from a single page.

        Args:
            doc: PyMuPDF document
            page_num: Page number (0-based)

        Returns:
            PDFPage object with extracted information
        """
        start_time = datetime.now()

        page = doc[page_num]

        # Extract text content
        text_content = page.get_text()

        # Get page dimensions
        rect = page.rect
        width = rect.width
        height = rect.height

        # Basic page type classification (will be refined by PageClassifierTool)
        page_type = self._classify_page_basic(text_content, page)

        # Count detected elements
        tables_detected = self._count_tables(page)
        images_detected = self._count_images(page)
        charts_detected = self._count_charts(page)

        # Save page as image if needed
        image_path = None
        if page_type in [PageType.IMAGE_HEAVY, PageType.TABLE_HEAVY]:
            image_path = self._save_page_image(page, page_num)

        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        return PDFPage(
            page_number=page_num + 1,  # Convert to 1-based
            page_type=page_type,
            text_content=text_content,
            image_path=image_path,
            width=width,
            height=height,
            extraction_method="PyMuPDF",
            confidence_score=0.8,  # Basic extraction confidence
            processing_time_ms=processing_time,
            tables_detected=tables_detected,
            images_detected=images_detected,
            charts_detected=charts_detected
        )

    def _classify_page_basic(self, text_content: str, page: fitz.Page) -> PageType:
        """
        Basic page classification based on content.

        Args:
            text_content: Extracted text
            page: PyMuPDF page object

        Returns:
            Basic page type classification
        """
        if not text_content.strip():
            return PageType.IMAGE_HEAVY

        # Check for table of contents indicators
        if any(keyword in text_content.lower() for keyword in ['contents', 'table of contents', 'chapter']):
            return PageType.TOC

        # Check for cover page indicators
        if any(keyword in text_content.lower() for keyword in ['demographic and health survey', 'dhs', 'kenya']):
            if len(text_content.split()) < 100:  # Short text suggests cover
                return PageType.COVER

        # Count text vs other elements
        text_blocks = page.get_text("dict")["blocks"]
        text_block_count = sum(1 for block in text_blocks if "lines" in block)
        image_block_count = sum(1 for block in text_blocks if "ext" in block)

        if image_block_count > text_block_count:
            return PageType.IMAGE_HEAVY
        elif self._has_table_structure(text_content):
            return PageType.TABLE_HEAVY
        else:
            return PageType.TEXT_HEAVY

    def _has_table_structure(self, text: str) -> bool:
        """Check if text has table-like structure."""
        lines = text.split('\n')
        tab_lines = sum(1 for line in lines if '\t' in line or '  ' in line)
        # 30% of lines have table structure
        return tab_lines > len(lines) * 0.3

    def _count_tables(self, page: fitz.Page) -> int:
        """Count tables on the page."""
        # Simple heuristic - count table-like structures
        try:
            tables = page.find_tables()
            return len(tables)
        except:
            # Fallback: count based on text structure
            text = page.get_text()
            lines = text.split('\n')
            table_lines = sum(1 for line in lines if len(
                line.split()) > 3 and any(c.isdigit() for c in line))
            return 1 if table_lines > 5 else 0

    def _count_images(self, page: fitz.Page) -> int:
        """Count images on the page."""
        image_list = page.get_images()
        return len(image_list)

    def _count_charts(self, page: fitz.Page) -> int:
        """Count charts/graphs on the page."""
        # For now, assume charts are images with specific characteristics
        # This could be enhanced with ML-based detection
        return 0

    def _save_page_image(self, page: fitz.Page, page_num: int) -> Optional[str]:
        """
        Save page as image for further processing.

        Args:
            page: PyMuPDF page object
            page_num: Page number (0-based)

        Returns:
            Path to saved image or None if failed
        """
        try:
            # Create images directory
            images_dir = config.EXTRACTED_DATA_PATH / "images"
            images_dir.mkdir(parents=True, exist_ok=True)

            # Generate image filename
            image_path = images_dir / f"page_{page_num + 1:03d}.png"

            # Render page as image
            # 2x zoom for better quality
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            pix.save(str(image_path))

            return str(image_path)

        except Exception as e:
            logger.warning(
                f"Failed to save page {page_num + 1} as image: {str(e)}")
            return None
