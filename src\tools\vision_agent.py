"""Vision agent tool for processing images, charts, and maps in DHS PDFs."""

import re
import json
import base64
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
from pathlib import Path

import fitz  # PyMuPDF
from PIL import Image
from euriai import EuriaiClient

from ..models.extraction import PDFPage, PageType, ImageExtraction
from ..models.indicator import Indicator, IndicatorType, IndicatorSource
from ..models.county import KENYA_COUNTIES
from ..config import config

logger = logging.getLogger(__name__)


class VisionAgentTool:
    """Advanced tool for processing visual content in DHS PDFs using AI vision capabilities."""

    def __init__(self):
        """Initialize the vision agent."""
        self.client = EuriaiClient(
            api_key=config.EURI_API_KEY,
            model=config.DEFAULT_LLM_MODEL
        )

        self.county_names = list(KENYA_COUNTIES.keys())

        # Vision analysis prompt for charts and graphs
        self.chart_analysis_prompt = """
You are an expert data analyst specializing in extracting health indicators from charts, graphs, and visual data in Kenya's DHS reports.

## VISUAL ANALYSIS GUIDELINES:

### 1. CHART TYPE IDENTIFICATION
Identify the type of visualization:
- Bar charts, line graphs, pie charts
- Maps with county-level data
- Scatter plots, histograms
- Tables embedded in images
- Infographics with statistics

### 2. DATA EXTRACTION RULES
- Extract ALL numerical values visible in the chart
- Identify county names, regions, or geographic areas
- Capture indicator names from titles, legends, axes labels
- Note units of measurement (%, per 1000, counts)
- Extract trend information from line graphs
- Identify comparative data between counties/regions

### 3. COUNTY IDENTIFICATION
Valid Kenya counties: {counties}
- Look for county names in legends, labels, map regions
- Handle abbreviations and variations
- Identify regional groupings (Western, Central, Coast, etc.)

### 4. INDICATOR CLASSIFICATION
Map visual indicators to these categories:
- child_health, maternal_health, nutrition, immunization
- family_planning, hiv_aids, malaria, water_sanitation
- demographic, socioeconomic, other

### 5. QUALITY ASSURANCE
- Verify extracted values match visual representation
- Cross-reference county names with provided list
- Ensure indicator names are descriptive and specific
- Note any data quality issues or unclear elements

## IMAGE DESCRIPTION:
{image_description}

## OUTPUT FORMAT:
Return a JSON object with extracted data:

```json
{{
    "image_analysis": {{
        "chart_type": "bar_chart/line_graph/map/pie_chart/table/infographic",
        "title": "Chart or figure title",
        "data_quality": "high/medium/low",
        "counties_identified": [],
        "indicators_found": [],
        "extraction_confidence": 0.85
    }},
    "indicators": [
        {{
            "name": "Specific indicator name",
            "type": "indicator_category",
            "county": "County name or 'National' for country-level",
            "year": 2022,
            "value": 25.3,
            "unit": "% or per 1000 or count",
            "age_group": "Age specification if visible",
            "gender": "Gender specification if visible",
            "urban_rural": "urban/rural/total",
            "chart_position": "Description of where data appears in chart",
            "note": "Additional context from chart",
            "summary": "One-sentence summary for embedding"
        }}
    ],
    "visual_elements": {{
        "legend_items": [],
        "axis_labels": [],
        "data_series": [],
        "annotations": []
    }},
    "extraction_notes": [
        "Any issues, assumptions, or observations"
    ]
}}
```

## CRITICAL REQUIREMENTS:
1. Extract ALL visible numerical data points
2. Verify county names against the provided list
3. Provide accurate chart position descriptions
4. Assess extraction confidence honestly
5. Generate meaningful summaries for embedding

Analyze the visual content:
"""

        # Map analysis prompt for geographic visualizations
        self.map_analysis_prompt = """
You are an expert in analyzing geographic maps and spatial data visualizations from Kenya's DHS reports.

## MAP ANALYSIS GUIDELINES:

### 1. MAP TYPE IDENTIFICATION
- Choropleth maps with county-level coloring
- Point maps with location markers
- Regional boundary maps
- Thematic maps with health indicators

### 2. SPATIAL DATA EXTRACTION
- Identify counties by shape, position, and labels
- Extract color-coded values from legends
- Map colors/patterns to numerical ranges
- Identify regional patterns and clusters

### 3. LEGEND INTERPRETATION
- Extract value ranges from color legends
- Map specific counties to their indicator values
- Identify missing data (gray/white areas)
- Note data classification methods (quintiles, quartiles)

### 4. COUNTY MAPPING
Valid Kenya counties: {counties}
- Match map regions to county names
- Handle county boundary changes over time
- Identify regional groupings and patterns

## MAP DESCRIPTION:
{map_description}

## OUTPUT FORMAT:
Return a JSON object with spatial data:

```json
{{
    "map_analysis": {{
        "map_type": "choropleth/point/boundary/thematic",
        "indicator_mapped": "Primary indicator shown",
        "legend_type": "continuous/categorical/quintiles",
        "data_classification": "Description of value ranges",
        "counties_visible": [],
        "missing_data_counties": []
    }},
    "county_values": [
        {{
            "county": "County name",
            "value_range": "e.g., 20-25%",
            "estimated_value": 22.5,
            "confidence": "high/medium/low",
            "color_code": "Description of color/pattern"
        }}
    ],
    "spatial_patterns": [
        "Regional pattern 1",
        "Spatial cluster 2"
    ]
}}
```

Analyze the map:
"""

    def extract_images_from_page(self, page: PDFPage, document_name: str) -> List[ImageExtraction]:
        """
        Extract all images from a PDF page.

        Args:
            page: PDFPage object
            document_name: Source document name

        Returns:
            List of ImageExtraction objects
        """
        if page.page_type not in [PageType.IMAGE_HEAVY, PageType.MIXED]:
            return []

        try:
            # Load the page using PyMuPDF for image extraction
            if page.image_path and Path(page.image_path).exists():
                # Use existing page image
                images = [self._create_image_extraction_from_page(
                    page, document_name)]
            else:
                # Extract images directly from PDF
                doc = fitz.open(document_name) if isinstance(
                    document_name, str) else None
                if doc and page.page_number <= len(doc):
                    pdf_page = doc[page.page_number - 1]
                    images = self._extract_images_pymupdf(
                        pdf_page, page.page_number, document_name)
                    doc.close()
                else:
                    images = []

            logger.info(
                f"Extracted {len(images)} images from page {page.page_number}")
            return images

        except Exception as e:
            logger.error(
                f"Failed to extract images from page {page.page_number}: {str(e)}")
            return []

    def _create_image_extraction_from_page(self, page: PDFPage, document_name: str) -> ImageExtraction:
        """Create ImageExtraction from page image."""
        return ImageExtraction(
            image_id=f"page_{page.page_number}_full",
            image_type="page_image",
            image_path=page.image_path,
            caption=f"Page {page.page_number} content",
            page_number=page.page_number
        )

    def _extract_images_pymupdf(self, pdf_page: fitz.Page, page_number: int, document_name: str) -> List[ImageExtraction]:
        """Extract images using PyMuPDF."""
        images = []

        try:
            image_list = pdf_page.get_images()

            for i, img in enumerate(image_list):
                try:
                    # Extract image
                    xref = img[0]
                    pix = fitz.Pixmap(pdf_page.parent, xref)

                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        # Save image
                        image_path = self._save_extracted_image(
                            pix, page_number, i)

                        if image_path:
                            # Get image position
                            img_rect = pdf_page.get_image_rects(xref)
                            position = None
                            if img_rect:
                                rect = img_rect[0]
                                position = {
                                    "x0": rect.x0, "y0": rect.y0,
                                    "x1": rect.x1, "y1": rect.y1
                                }

                            image_extraction = ImageExtraction(
                                image_id=f"img_{page_number}_{i+1}",
                                image_type="extracted_image",
                                image_path=image_path,
                                page_number=page_number,
                                position=position,
                                dimensions={"width": pix.width,
                                            "height": pix.height}
                            )

                            images.append(image_extraction)

                    pix = None  # Free memory

                except Exception as e:
                    logger.warning(
                        f"Failed to extract image {i+1} from page {page_number}: {str(e)}")
                    continue

        except Exception as e:
            logger.warning(f"PyMuPDF image extraction failed: {str(e)}")

        return images

    def _save_extracted_image(self, pix: fitz.Pixmap, page_number: int, image_index: int) -> Optional[str]:
        """Save extracted image to file."""
        try:
            # Create images directory
            images_dir = config.EXTRACTED_DATA_PATH / "images"
            images_dir.mkdir(parents=True, exist_ok=True)

            # Generate image filename
            image_path = images_dir / \
                f"page_{page_number:03d}_img_{image_index+1:02d}.png"

            # Save image
            pix.save(str(image_path))

            return str(image_path)

        except Exception as e:
            logger.warning(f"Failed to save extracted image: {str(e)}")
            return None

    def analyze_image_content(self, image: ImageExtraction, document_name: str, year: int = 2022) -> List[Indicator]:
        """
        Analyze image content and extract indicators using AI vision.

        Args:
            image: ImageExtraction object
            document_name: Source document name
            year: Survey year

        Returns:
            List of extracted Indicator objects
        """
        try:
            # Determine analysis type based on image characteristics
            if self._is_map_image(image):
                return self._analyze_map_image(image, document_name, year)
            else:
                return self._analyze_chart_image(image, document_name, year)

        except Exception as e:
            logger.error(f"Failed to analyze image {image.image_id}: {str(e)}")
            return []

    def _is_map_image(self, image: ImageExtraction) -> bool:
        """Determine if image is likely a map."""
        # Simple heuristics - could be enhanced with ML
        if image.caption:
            map_keywords = ['map', 'county', 'region', 'geographic', 'spatial']
            return any(keyword in image.caption.lower() for keyword in map_keywords)

        # Default to chart analysis
        return False

    def _analyze_chart_image(self, image: ImageExtraction, document_name: str, year: int) -> List[Indicator]:
        """Analyze chart/graph image."""
        try:
            # For now, use a simplified approach without actual image processing
            # In production, this would use vision models to analyze the actual image

            # Generate description based on available metadata
            image_description = self._generate_image_description(image)

            prompt = self.chart_analysis_prompt.format(
                counties=", ".join(self.county_names),
                image_description=image_description
            )

            # Get AI analysis (note: this is text-based analysis of description)
            response = self.client.generate_completion(
                prompt=prompt,
                temperature=0.1,
                max_tokens=2000
            )

            # Extract content from response
            if isinstance(response, dict) and 'choices' in response:
                content = response['choices'][0]['message']['content']
            else:
                content = response

            # Parse response
            indicators = self._parse_vision_analysis_response(
                content, image, document_name, year)

            logger.info(
                f"Extracted {len(indicators)} indicators from image {image.image_id}")
            return indicators

        except Exception as e:
            logger.error(f"Failed to analyze chart image: {str(e)}")
            return []

    def _analyze_map_image(self, image: ImageExtraction, document_name: str, year: int) -> List[Indicator]:
        """Analyze map image."""
        try:
            # Generate map description
            map_description = self._generate_map_description(image)

            prompt = self.map_analysis_prompt.format(
                counties=", ".join(self.county_names),
                map_description=map_description
            )

            # Get AI analysis
            response = self.client.generate_completion(
                prompt=prompt,
                temperature=0.1,
                max_tokens=2000
            )

            # Extract content from response
            if isinstance(response, dict) and 'choices' in response:
                content = response['choices'][0]['message']['content']
            else:
                content = response

            # Parse map analysis response
            indicators = self._parse_map_analysis_response(
                content, image, document_name, year)

            logger.info(
                f"Extracted {len(indicators)} indicators from map {image.image_id}")
            return indicators

        except Exception as e:
            logger.error(f"Failed to analyze map image: {str(e)}")
            return []

    def _generate_image_description(self, image: ImageExtraction) -> str:
        """Generate description of image for analysis."""
        description_parts = [
            f"Image ID: {image.image_id}",
            f"Image Type: {image.image_type}",
            f"Page Number: {image.page_number}"
        ]

        if image.caption:
            description_parts.append(f"Caption: {image.caption}")

        if image.dimensions:
            description_parts.append(
                f"Dimensions: {image.dimensions['width']}x{image.dimensions['height']}")

        if image.detected_text:
            description_parts.append(f"Detected Text: {image.detected_text}")

        # Note: In production, this would include actual image analysis
        description_parts.append(
            "Note: This is a placeholder description. In production, this would include actual visual analysis of the image content.")

        return "\n".join(description_parts)

    def _generate_map_description(self, image: ImageExtraction) -> str:
        """Generate description of map for analysis."""
        description_parts = [
            f"Map ID: {image.image_id}",
            f"Page Number: {image.page_number}",
            "Map Type: Geographic visualization of Kenya"
        ]

        if image.caption:
            description_parts.append(f"Caption: {image.caption}")

        # Note: In production, this would include actual map analysis
        description_parts.append(
            "Note: This is a placeholder description. In production, this would include actual visual analysis of the map content, county boundaries, and color coding.")

        return "\n".join(description_parts)

    def _parse_vision_analysis_response(self, response: str, image: ImageExtraction,
                                        document_name: str, year: int) -> List[Indicator]:
        """Parse vision analysis response into Indicator objects."""
        indicators = []

        try:
            # Extract JSON from response
            json_str = self._extract_json_from_response(response)
            if not json_str:
                logger.warning(
                    f"No JSON found in vision analysis response for {image.image_id}")
                return []

            data = json.loads(json_str)

            if 'indicators' not in data:
                logger.warning(
                    f"No 'indicators' key found in response for {image.image_id}")
                return []

            # Process each indicator
            for ind_data in data['indicators']:
                try:
                    indicator = self._create_indicator_from_vision_data(
                        ind_data, image, document_name, year
                    )
                    if indicator and self._validate_vision_indicator(indicator):
                        indicators.append(indicator)
                except Exception as e:
                    logger.warning(
                        f"Failed to create indicator from vision data: {str(e)}")
                    continue

        except json.JSONDecodeError as e:
            logger.warning(
                f"Failed to parse JSON from vision analysis: {str(e)}")
        except Exception as e:
            logger.error(f"Error parsing vision analysis response: {str(e)}")

        return indicators

    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """Extract JSON from response using multiple strategies."""
        # Strategy 1: Look for JSON code block
        json_match = re.search(
            r'```json\s*(\{.*?\})\s*```', response, re.DOTALL)
        if json_match:
            return json_match.group(1)

        # Strategy 2: Look for any JSON object
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json_match.group(0)

        return None

    def _create_indicator_from_vision_data(self, data: Dict[str, Any], image: ImageExtraction,
                                           document_name: str, year: int) -> Optional[Indicator]:
        """Create Indicator object from vision analysis data."""
        try:
            # Validate required fields
            if not all(key in data for key in ['name', 'county']):
                return None

            # Validate and normalize county name
            county = self._normalize_county_name(data['county'])
            if not county and data['county'].lower() != 'national':
                return None

            # Use 'National' for country-level data
            if data['county'].lower() == 'national':
                county = 'National'

            # Map indicator type
            indicator_type = self._map_indicator_type(
                data.get('type', 'other'))

            # Create source information
            source = IndicatorSource(
                document=document_name,
                page_number=image.page_number,
                figure_id=image.image_id,
                section=f"Figure: {image.caption or 'Visual Content'}"
            )

            # Create indicator
            indicator = Indicator(
                name=data['name'],
                type=indicator_type,
                county=county,
                year=data.get('year', year),
                value=float(data['value']) if data.get(
                    'value') is not None else None,
                value_text=str(data.get('value', '')),
                unit=data.get('unit'),
                age_group=data.get('age_group'),
                gender=data.get('gender'),
                urban_rural=data.get('urban_rural'),
                source=source,
                note=data.get('note'),
                extraction_method="VisionAgentTool",
                extraction_confidence=0.7,  # Medium confidence for vision data
                summary=data.get(
                    'summary', f"{data['name']} in {county} ({year}): {data.get('value', 'N/A')}")
            )

            return indicator

        except Exception as e:
            logger.warning(
                f"Failed to create indicator from vision data: {str(e)}")
            return None

    def _normalize_county_name(self, county_name: str) -> Optional[str]:
        """Normalize and validate county name."""
        if not county_name:
            return None

        county_name = county_name.strip().title()

        # Direct match
        if county_name in self.county_names:
            return county_name

        # Fuzzy matching
        county_lower = county_name.lower()
        for county in self.county_names:
            if county.lower() == county_lower:
                return county
            if county_lower in county.lower() or county.lower() in county_lower:
                return county

        return None

    def _map_indicator_type(self, type_str: str) -> IndicatorType:
        """Map string to IndicatorType enum."""
        try:
            return IndicatorType(type_str.lower())
        except ValueError:
            return IndicatorType.OTHER

    def _validate_vision_indicator(self, indicator: Indicator) -> bool:
        """Validate vision-extracted indicator."""
        # Required fields validation
        if not indicator.name or not indicator.county:
            return False

        # County name validation (allow 'National')
        if indicator.county != 'National' and indicator.county not in self.county_names:
            return False

        # Year validation
        if indicator.year < 1990 or indicator.year > 2030:
            return False

        return True
