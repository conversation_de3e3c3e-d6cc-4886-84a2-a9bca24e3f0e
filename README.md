# DHS RAQ System - Retrieval-Augmented Questioning for DHS PDFs

A comprehensive system for extracting, storing, and querying health indicators from DHS (Demographic and Health Survey) PDFs using advanced AI and embedding technologies.

## 🎯 System Goals

Build a Retrieval-Augmented Questioning (RAQ) system that can:

### 🔹 Input Processing
- Parse multiple DHS PDFs (Volume 1, 2, Addendum)
- Extract text, table, image, and map-based indicators
- Store structured indicators by county, year, and type

### 🔹 Query & Retrieval
- Accept natural language questions
- Use embeddings for semantic retrieval across counties, indicators, and years
- Answer precise county-based queries like: *"What are the child health indicators for Kakamega in 2022 compared to Vihiga?"*

### 🔹 Output Generation
- Structured tables and summary narratives
- Visual charts and interactive maps
- Formatted comparison reports

## 🏗️ Architecture

### Component Layers
| Layer | Description |
|-------|-------------|
| **Extraction Layer** | Extract data from text, tables, images, and maps |
| **Storage Layer** | Store structured indicators per county/year/type |
| **Embedding Layer** | Embed indicator metadata for semantic search |
| **Query Layer** | Handle natural language questions and generate answers |
| **Visualization Layer** | Output charts, maps, summary reports |
| **UI Layer** | Streamlit app with PDF upload and query interface |

### 🤖 Agents (2 total)
- **RAQAgent**: Main extraction and merge pipeline
- **QueryAgent**: NLQ processing + embedding-based retrieval + formatted answers

### 🛠️ Tools (12 total)
- **PDFLoaderTool**: Load PDFs and split into pages
- **PageClassifierTool**: Classify pages as text/table/image-heavy
- **TextAgentTool**: Extract indicators from textual pages
- **TableExtractorTool**: Extract table title, body, and footnote
- **VisionAgentTool**: Handle graphs/maps/scanned tables
- **MapParserTool**: Extract county values from image maps
- **IndicatorMerger**: Merge and normalize all indicators
- **EmbeddingGeneratorTool**: Convert indicator metadata into embeddings
- **VectorStoreTool**: Store and retrieve embeddings (FAISS/Chroma)
- **QueryAgentTool**: Accept NLQ, retrieve context, format results
- **ChartRendererTool**: Generate charts from filtered data
- **ChoroplethMapTool**: Create geo-visualizations with Kenyan coordinates

## 🚀 Quick Start

### 1. Installation
```bash
pip install -r requirements.txt
```

### 2. Configuration
```bash
cp .env.template .env
# Edit .env with your EURI_API_KEY
```

### 3. Run the Application
```bash
streamlit run app.py
```

## 📊 Example Usage

### Query Examples
- *"Compare stunting and immunization coverage in Kakamega vs Bungoma in 2022"*
- *"Show me child health indicators for all counties in Western region"*
- *"What are the trends in maternal mortality from 2014 to 2022?"*

### Output Format
- ✅ Well-formatted comparison tables
- ✅ 📊 Interactive bar/line charts
- ✅ 📝 AI-generated summary narratives
- ✅ 🗺️ Choropleth maps with county-level data

## 🧱 Technology Stack

| Component | Technology |
|-----------|------------|
| **LLM** | Euriai SDK (GPT-4.1-nano, Gemini-2.0-flash) |
| **Embedding** | text-embedding-3-small via Euriai |
| **Vector DB** | FAISS / Chroma |
| **UI** | Streamlit |
| **Visualization** | Plotly / Altair / Folium |
| **Geo Mapping** | Kenyan counties GeoJSON |

## 📁 Project Structure

```
kdhs/
├── src/
│   ├── models/          # Pydantic data models
│   ├── tools/           # Processing and extraction tools
│   ├── agents/          # Main orchestration agents
│   └── utils/           # Utility functions
├── data/
│   ├── uploads/         # PDF upload directory
│   ├── extracted/       # Processed indicator data
│   ├── vector_store/    # Embedding storage
│   └── geo/            # GeoJSON files
├── tests/              # Test files
├── app.py             # Streamlit application
└── requirements.txt   # Dependencies
```

## 🔧 Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black src/
flake8 src/
```

## 📄 License

MIT License - see LICENSE file for details.
