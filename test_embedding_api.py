"""Test embedding generation with real API key."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_embedding_generation():
    """Test embedding generation with real API."""
    print("🧠 Testing Embedding Generation with API")
    print("=" * 50)
    
    try:
        from src.config import config
        from src.tools.embedding_generator import EmbeddingGeneratorTool
        from src.models.indicator import Indicator, IndicatorType, IndicatorSource
        
        print(f"API Key configured: {config.EURI_API_KEY[:20]}...")
        
        # Initialize embedding generator
        generator = EmbeddingGeneratorTool()
        print("✓ Embedding generator initialized")
        
        # Test single embedding
        test_text = "Stunting prevalence in Kakamega County was 22.3% in 2022 among children under 5 years"
        print(f"Generating embedding for: '{test_text[:50]}...'")
        
        embedding = generator.generate_embedding(test_text)
        
        print(f"✓ Embedding generated: {len(embedding)} dimensions")
        print(f"✓ Sample values: {embedding[:5]}")
        
        # Test batch embeddings
        test_texts = [
            "Child health indicators in Western Kenya show improvement",
            "Maternal mortality rates decreased in urban areas",
            "Immunization coverage reached 85% in rural counties"
        ]
        
        print(f"\nGenerating batch embeddings for {len(test_texts)} texts...")
        embeddings = generator.generate_embeddings_batch(test_texts)
        
        print(f"✓ Batch embeddings generated: {len(embeddings)} vectors")
        print(f"✓ All vectors have {len(embeddings[0])} dimensions")
        
        # Test indicator embedding
        source = IndicatorSource(document="FR380.pdf", page_number=15)
        indicator = Indicator(
            name="Stunting prevalence",
            type=IndicatorType.NUTRITION,
            county="Kakamega",
            year=2022,
            value=22.3,
            unit="%",
            age_group="0-59 months",
            source=source,
            summary="Stunting prevalence among children under 5 in Kakamega County was 22.3% in 2022"
        )
        
        print(f"\nCreating embedding record for indicator...")
        record = generator.create_embedding_record(indicator)
        
        print(f"✓ Indicator embedding record created")
        print(f"✓ Record ID: {record.id}")
        print(f"✓ Content: {record.content[:100]}...")
        print(f"✓ Embedding dimensions: {len(record.embedding)}")
        
        # Test vector store integration
        from src.tools.vector_store import VectorStoreTool
        
        store = VectorStoreTool()
        store.clear()
        store.add_records([record])
        
        stats = store.get_stats()
        print(f"\n✓ Vector store integration successful")
        print(f"✓ Records stored: {stats['total_records']}")
        
        # Test similarity search
        query_embedding = generator.generate_embedding("nutrition indicators in Kakamega")
        results = store.search_by_embedding(query_embedding, top_k=1)
        
        if results:
            print(f"✓ Similarity search successful")
            print(f"✓ Top result similarity: {results[0].similarity_score:.4f}")
            print(f"✓ Retrieved: {results[0].record.indicator_name}")
        
        store.clear()
        
        print("\n🎉 EMBEDDING API TEST PASSED!")
        print("✓ Single embedding generation")
        print("✓ Batch embedding generation") 
        print("✓ Indicator embedding records")
        print("✓ Vector store integration")
        print("✓ Similarity search")
        
        return True
        
    except Exception as e:
        print(f"\n❌ EMBEDDING API TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_embedding_generation()
    sys.exit(0 if success else 1)
