"""Embedding generation tool using Euriai SDK."""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from euriai.langchain_embed import EuriaiEmbeddings
from ..models.indicator import Indicator
from ..models.embedding import EmbeddingRecord
from ..config import config

logger = logging.getLogger(__name__)


class EmbeddingGeneratorTool:
    """Tool for generating embeddings using Euriai SDK."""
    
    def __init__(self):
        """Initialize the embedding generator."""
        self.embedding_model = EuriaiEmbeddings(
            api_key=config.EURI_API_KEY,
            model=config.DEFAULT_EMBEDDING_MODEL
        )
        
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text to embed
            
        Returns:
            List of embedding values
        """
        try:
            embedding = self.embedding_model.embed_query(text)
            return embedding
        except Exception as e:
            logger.error(f"Failed to generate embedding: {str(e)}")
            raise
    
    def generate_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        try:
            embeddings = self.embedding_model.embed_documents(texts)
            return embeddings
        except Exception as e:
            logger.error(f"Failed to generate batch embeddings: {str(e)}")
            raise
    
    def create_embedding_record(self, indicator: Indicator) -> EmbeddingRecord:
        """
        Create an embedding record from an indicator.
        
        Args:
            indicator: Indicator to embed
            
        Returns:
            EmbeddingRecord with generated embedding
        """
        # Generate text for embedding
        embedding_text = indicator.to_embedding_text()
        
        # Generate embedding
        embedding = self.generate_embedding(embedding_text)
        
        # Create record
        record = EmbeddingRecord(
            id=f"ind_{indicator.county}_{indicator.year}_{hash(indicator.name)}",
            embedding=embedding,
            indicator_id=indicator.id or f"temp_{hash(embedding_text)}",
            county=indicator.county,
            year=indicator.year,
            indicator_type=indicator.type.value,
            indicator_name=indicator.name,
            content=embedding_text,
            metadata={
                "value": indicator.value,
                "unit": indicator.unit,
                "age_group": indicator.age_group,
                "gender": indicator.gender,
                "urban_rural": indicator.urban_rural,
                "source_document": indicator.source.document,
                "source_page": indicator.source.page_number,
                "extraction_method": indicator.extraction_method,
                "extraction_confidence": indicator.extraction_confidence
            }
        )
        
        return record
    
    def create_embedding_records_batch(self, indicators: List[Indicator]) -> List[EmbeddingRecord]:
        """
        Create embedding records for multiple indicators.
        
        Args:
            indicators: List of indicators to embed
            
        Returns:
            List of EmbeddingRecord objects
        """
        logger.info(f"Creating embeddings for {len(indicators)} indicators")
        
        # Prepare texts for batch embedding
        texts = [indicator.to_embedding_text() for indicator in indicators]
        
        # Generate embeddings in batch
        embeddings = self.generate_embeddings_batch(texts)
        
        # Create records
        records = []
        for indicator, embedding in zip(indicators, embeddings):
            record = EmbeddingRecord(
                id=f"ind_{indicator.county}_{indicator.year}_{hash(indicator.name)}",
                embedding=embedding,
                indicator_id=indicator.id or f"temp_{hash(indicator.to_embedding_text())}",
                county=indicator.county,
                year=indicator.year,
                indicator_type=indicator.type.value,
                indicator_name=indicator.name,
                content=indicator.to_embedding_text(),
                metadata={
                    "value": indicator.value,
                    "unit": indicator.unit,
                    "age_group": indicator.age_group,
                    "gender": indicator.gender,
                    "urban_rural": indicator.urban_rural,
                    "source_document": indicator.source.document,
                    "source_page": indicator.source.page_number,
                    "extraction_method": indicator.extraction_method,
                    "extraction_confidence": indicator.extraction_confidence
                }
            )
            records.append(record)
        
        logger.info(f"Created {len(records)} embedding records")
        return records
