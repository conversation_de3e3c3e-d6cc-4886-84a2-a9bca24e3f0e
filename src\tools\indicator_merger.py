"""Indicator merger tool for normalizing and consolidating health indicators from multiple sources."""

import re
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime
import logging
from collections import defaultdict

from ..models.indicator import Indicator, IndicatorType
from ..models.county import KENYA_COUNTIES
from ..config import config

logger = logging.getLogger(__name__)


class IndicatorMerger:
    """Tool for merging, normalizing, and deduplicating health indicators from multiple extraction sources."""
    
    def __init__(self):
        """Initialize the indicator merger."""
        self.county_names = list(KENYA_COUNTIES.keys())
        
        # Indicator name normalization patterns
        self.normalization_patterns = {
            # Nutrition indicators
            r'stunt(ing|ed)?\s*(prevalence|rate)?': 'Stunting prevalence',
            r'wast(ing|ed)?\s*(prevalence|rate)?': 'Wasting prevalence',
            r'underweight\s*(prevalence|rate)?': 'Underweight prevalence',
            r'anemia\s*(prevalence|rate)?': 'Anemia prevalence',
            
            # Immunization indicators
            r'(full|complete)\s*immuniz(ation|ed)\s*(coverage|rate)?': 'Full immunization coverage',
            r'bcg\s*(coverage|vaccination|rate)?': 'BCG vaccination coverage',
            r'dpt\s*(coverage|vaccination|rate)?': 'DPT vaccination coverage',
            r'measles\s*(coverage|vaccination|rate)?': 'Measles vaccination coverage',
            r'polio\s*(coverage|vaccination|rate)?': 'Polio vaccination coverage',
            
            # Maternal health indicators
            r'antenatal\s*care\s*(coverage|visits)?': 'Antenatal care coverage',
            r'skilled\s*birth\s*attend(ance|ant)': 'Skilled birth attendance',
            r'maternal\s*mortality\s*(rate|ratio)?': 'Maternal mortality rate',
            r'postnatal\s*care\s*(coverage|visits)?': 'Postnatal care coverage',
            
            # Child health indicators
            r'child\s*mortality\s*(rate)?': 'Child mortality rate',
            r'infant\s*mortality\s*(rate)?': 'Infant mortality rate',
            r'neonatal\s*mortality\s*(rate)?': 'Neonatal mortality rate',
            
            # Family planning indicators
            r'contraceptive\s*(use|prevalence|rate)?': 'Contraceptive use',
            r'family\s*planning\s*(use|coverage)?': 'Family planning use',
            r'unmet\s*need\s*(family\s*planning)?': 'Unmet need for family planning',
            
            # HIV/AIDS indicators
            r'hiv\s*(prevalence|rate)?': 'HIV prevalence',
            r'aids\s*(prevalence|rate)?': 'AIDS prevalence',
            r'hiv\s*testing\s*(coverage|rate)?': 'HIV testing coverage',
            
            # Malaria indicators
            r'malaria\s*(prevalence|rate)?': 'Malaria prevalence',
            r'itn\s*(use|coverage)?': 'ITN use',
            r'bed\s*net\s*(use|coverage)?': 'Bed net use',
            
            # Water and sanitation
            r'improved\s*water\s*(access|coverage)?': 'Improved water access',
            r'improved\s*sanitation\s*(access|coverage)?': 'Improved sanitation access',
        }
        
        # Unit normalization
        self.unit_patterns = {
            r'percent|%': '%',
            r'per\s*1000|per\s*thousand': 'per 1000',
            r'per\s*100000|per\s*hundred\s*thousand': 'per 100000',
            r'ratio': 'ratio',
            r'count|number': 'count'
        }

    def merge_indicators(self, indicator_lists: List[List[Indicator]], 
                        merge_strategy: str = "comprehensive") -> List[Indicator]:
        """
        Merge indicators from multiple sources with deduplication and normalization.
        
        Args:
            indicator_lists: List of indicator lists from different sources
            merge_strategy: Strategy for merging ("comprehensive", "conservative", "latest")
            
        Returns:
            List of merged and normalized indicators
        """
        logger.info(f"Merging indicators from {len(indicator_lists)} sources using {merge_strategy} strategy")
        
        # Flatten all indicators
        all_indicators = []
        for indicator_list in indicator_lists:
            all_indicators.extend(indicator_list)
        
        logger.info(f"Total indicators before merging: {len(all_indicators)}")
        
        # Step 1: Normalize indicator names and units
        normalized_indicators = self._normalize_indicators(all_indicators)
        
        # Step 2: Group similar indicators
        grouped_indicators = self._group_similar_indicators(normalized_indicators)
        
        # Step 3: Merge duplicates within groups
        merged_indicators = self._merge_duplicate_indicators(grouped_indicators, merge_strategy)
        
        # Step 4: Validate and clean final results
        final_indicators = self._validate_merged_indicators(merged_indicators)
        
        logger.info(f"Final indicators after merging: {len(final_indicators)}")
        logger.info(f"Deduplication rate: {(len(all_indicators) - len(final_indicators)) / len(all_indicators) * 100:.1f}%")
        
        return final_indicators

    def _normalize_indicators(self, indicators: List[Indicator]) -> List[Indicator]:
        """Normalize indicator names, units, and other attributes."""
        normalized = []
        
        for indicator in indicators:
            try:
                # Create a copy to avoid modifying original
                normalized_indicator = Indicator(
                    id=indicator.id,
                    name=self._normalize_indicator_name(indicator.name),
                    type=indicator.type,
                    county=self._normalize_county_name(indicator.county),
                    year=indicator.year,
                    value=indicator.value,
                    value_text=indicator.value_text,
                    unit=self._normalize_unit(indicator.unit),
                    age_group=self._normalize_age_group(indicator.age_group),
                    gender=self._normalize_gender(indicator.gender),
                    urban_rural=self._normalize_urban_rural(indicator.urban_rural),
                    source=indicator.source,
                    confidence_interval=indicator.confidence_interval,
                    sample_size=indicator.sample_size,
                    note=indicator.note,
                    extraction_method=indicator.extraction_method,
                    extraction_confidence=indicator.extraction_confidence,
                    created_at=indicator.created_at,
                    summary=indicator.summary,
                    keywords=indicator.keywords
                )
                
                if normalized_indicator.county:  # Only include if county is valid
                    normalized.append(normalized_indicator)
                    
            except Exception as e:
                logger.warning(f"Failed to normalize indicator {indicator.name}: {str(e)}")
                continue
        
        logger.info(f"Normalized {len(normalized)} indicators")
        return normalized

    def _normalize_indicator_name(self, name: str) -> str:
        """Normalize indicator name using pattern matching."""
        if not name:
            return "Unknown Indicator"
        
        name_lower = name.lower().strip()
        
        # Apply normalization patterns
        for pattern, normalized_name in self.normalization_patterns.items():
            if re.search(pattern, name_lower):
                return normalized_name
        
        # If no pattern matches, clean up the original name
        cleaned_name = re.sub(r'\s+', ' ', name.strip())
        return cleaned_name.title()

    def _normalize_county_name(self, county: str) -> Optional[str]:
        """Normalize and validate county name."""
        if not county:
            return None
        
        county = county.strip().title()
        
        # Handle special cases
        if county.lower() == 'national':
            return 'National'
        
        # Direct match
        if county in self.county_names:
            return county
        
        # Fuzzy matching
        county_lower = county.lower()
        for valid_county in self.county_names:
            if valid_county.lower() == county_lower:
                return valid_county
            if county_lower in valid_county.lower() or valid_county.lower() in county_lower:
                return valid_county
        
        logger.warning(f"Could not normalize county name: {county}")
        return None

    def _normalize_unit(self, unit: Optional[str]) -> Optional[str]:
        """Normalize unit of measurement."""
        if not unit:
            return None
        
        unit_lower = unit.lower().strip()
        
        # Apply unit normalization patterns
        for pattern, normalized_unit in self.unit_patterns.items():
            if re.search(pattern, unit_lower):
                return normalized_unit
        
        return unit.strip()

    def _normalize_age_group(self, age_group: Optional[str]) -> Optional[str]:
        """Normalize age group specification."""
        if not age_group:
            return None
        
        age_group = age_group.strip()
        
        # Common age group normalizations
        age_normalizations = {
            r'under\s*5|<\s*5|0-5': '0-59 months',
            r'6-59\s*months': '6-59 months',
            r'12-23\s*months': '12-23 months',
            r'15-49\s*(years)?': '15-49 years',
            r'20-49\s*(years)?': '20-49 years',
            r'children': 'children',
            r'women': 'women',
            r'all\s*ages?': 'all ages'
        }
        
        age_lower = age_group.lower()
        for pattern, normalized in age_normalizations.items():
            if re.search(pattern, age_lower):
                return normalized
        
        return age_group

    def _normalize_gender(self, gender: Optional[str]) -> Optional[str]:
        """Normalize gender specification."""
        if not gender:
            return None
        
        gender_lower = gender.lower().strip()
        
        gender_map = {
            'male': 'men',
            'female': 'women',
            'men': 'men',
            'women': 'women',
            'children': 'children',
            'all': 'all',
            'both': 'all'
        }
        
        return gender_map.get(gender_lower, gender)

    def _normalize_urban_rural(self, urban_rural: Optional[str]) -> Optional[str]:
        """Normalize urban/rural specification."""
        if not urban_rural:
            return None
        
        ur_lower = urban_rural.lower().strip()
        
        ur_map = {
            'urban': 'urban',
            'rural': 'rural',
            'total': 'total',
            'all': 'total',
            'both': 'total'
        }
        
        return ur_map.get(ur_lower, urban_rural)

    def _group_similar_indicators(self, indicators: List[Indicator]) -> Dict[str, List[Indicator]]:
        """Group indicators that represent the same measurement."""
        groups = defaultdict(list)
        
        for indicator in indicators:
            # Create grouping key based on normalized attributes
            key = self._create_grouping_key(indicator)
            groups[key].append(indicator)
        
        logger.info(f"Grouped indicators into {len(groups)} unique groups")
        return dict(groups)

    def _create_grouping_key(self, indicator: Indicator) -> str:
        """Create a key for grouping similar indicators."""
        key_parts = [
            indicator.name.lower(),
            indicator.county.lower() if indicator.county else 'unknown',
            str(indicator.year),
            indicator.type.value,
            indicator.age_group.lower() if indicator.age_group else 'all',
            indicator.gender.lower() if indicator.gender else 'all',
            indicator.urban_rural.lower() if indicator.urban_rural else 'total'
        ]
        
        return "|".join(key_parts)

    def _merge_duplicate_indicators(self, grouped_indicators: Dict[str, List[Indicator]], 
                                  merge_strategy: str) -> List[Indicator]:
        """Merge duplicate indicators within each group."""
        merged = []
        
        for group_key, indicators in grouped_indicators.items():
            if len(indicators) == 1:
                # No duplicates, keep as is
                merged.append(indicators[0])
            else:
                # Merge duplicates
                merged_indicator = self._merge_indicator_group(indicators, merge_strategy)
                if merged_indicator:
                    merged.append(merged_indicator)
        
        return merged

    def _merge_indicator_group(self, indicators: List[Indicator], strategy: str) -> Optional[Indicator]:
        """Merge a group of duplicate indicators."""
        if not indicators:
            return None
        
        if len(indicators) == 1:
            return indicators[0]
        
        logger.debug(f"Merging {len(indicators)} duplicate indicators: {indicators[0].name}")
        
        if strategy == "latest":
            # Use the most recently created indicator
            return max(indicators, key=lambda x: x.created_at)
        
        elif strategy == "conservative":
            # Use the indicator with highest extraction confidence
            return max(indicators, key=lambda x: x.extraction_confidence or 0)
        
        elif strategy == "comprehensive":
            # Merge information from all indicators
            return self._comprehensive_merge(indicators)
        
        else:
            logger.warning(f"Unknown merge strategy: {strategy}, using conservative")
            return max(indicators, key=lambda x: x.extraction_confidence or 0)

    def _comprehensive_merge(self, indicators: List[Indicator]) -> Indicator:
        """Comprehensively merge multiple indicators."""
        # Use the indicator with highest confidence as base
        base_indicator = max(indicators, key=lambda x: x.extraction_confidence or 0)
        
        # Merge values - prefer non-null values
        merged_value = None
        merged_unit = None
        merged_confidence_interval = None
        merged_sample_size = None
        
        for indicator in indicators:
            if indicator.value is not None and merged_value is None:
                merged_value = indicator.value
            if indicator.unit and merged_unit is None:
                merged_unit = indicator.unit
            if indicator.confidence_interval and merged_confidence_interval is None:
                merged_confidence_interval = indicator.confidence_interval
            if indicator.sample_size and merged_sample_size is None:
                merged_sample_size = indicator.sample_size
        
        # Merge notes
        notes = [ind.note for ind in indicators if ind.note]
        merged_note = "; ".join(set(notes)) if notes else base_indicator.note
        
        # Merge extraction methods
        methods = [ind.extraction_method for ind in indicators if ind.extraction_method]
        merged_method = ", ".join(set(methods)) if methods else base_indicator.extraction_method
        
        # Calculate average confidence
        confidences = [ind.extraction_confidence for ind in indicators if ind.extraction_confidence]
        avg_confidence = sum(confidences) / len(confidences) if confidences else base_indicator.extraction_confidence
        
        # Create merged indicator
        merged_indicator = Indicator(
            id=base_indicator.id,
            name=base_indicator.name,
            type=base_indicator.type,
            county=base_indicator.county,
            year=base_indicator.year,
            value=merged_value,
            value_text=base_indicator.value_text,
            unit=merged_unit,
            age_group=base_indicator.age_group,
            gender=base_indicator.gender,
            urban_rural=base_indicator.urban_rural,
            source=base_indicator.source,
            confidence_interval=merged_confidence_interval,
            sample_size=merged_sample_size,
            note=merged_note,
            extraction_method=merged_method,
            extraction_confidence=avg_confidence,
            created_at=base_indicator.created_at,
            summary=base_indicator.summary,
            keywords=base_indicator.keywords
        )
        
        return merged_indicator

    def _validate_merged_indicators(self, indicators: List[Indicator]) -> List[Indicator]:
        """Validate and clean merged indicators."""
        valid_indicators = []
        
        for indicator in indicators:
            if self._is_valid_indicator(indicator):
                valid_indicators.append(indicator)
            else:
                logger.debug(f"Removed invalid indicator: {indicator.name}")
        
        return valid_indicators

    def _is_valid_indicator(self, indicator: Indicator) -> bool:
        """Check if indicator is valid."""
        # Required fields
        if not indicator.name or not indicator.county:
            return False
        
        # County validation
        if indicator.county != 'National' and indicator.county not in self.county_names:
            return False
        
        # Year validation
        if indicator.year < 1990 or indicator.year > 2030:
            return False
        
        # Value validation (if present)
        if indicator.value is not None:
            if indicator.value < 0 or indicator.value > 100000:
                return False
        
        return True

    def get_merge_statistics(self, original_count: int, merged_count: int) -> Dict[str, Any]:
        """Get statistics about the merge operation."""
        return {
            "original_indicators": original_count,
            "merged_indicators": merged_count,
            "duplicates_removed": original_count - merged_count,
            "deduplication_rate": (original_count - merged_count) / original_count * 100 if original_count > 0 else 0,
            "merge_timestamp": datetime.now().isoformat()
        }
